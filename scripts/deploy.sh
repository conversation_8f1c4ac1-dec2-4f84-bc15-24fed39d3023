#!/bin/bash

# 对讲机软件下载站部署脚本
# 使用方法: ./scripts/deploy.sh [environment]
# 环境: dev (开发) | prod (生产)

set -e

ENVIRONMENT=${1:-dev}
PROJECT_NAME="downloads-website"

echo "🚀 开始部署 $PROJECT_NAME ($ENVIRONMENT 环境)..."

# 检查 Docker 和 Docker Compose 是否安装
if ! command -v docker &> /dev/null; then
    echo "❌ Docker 未安装，请先安装 Docker"
    exit 1
fi

if ! command -v docker compose &> /dev/null; then
    echo "❌ Docker Compose 未安装，请先安装 Docker Compose"
    exit 1
fi

# 根据环境选择配置文件
if [ "$ENVIRONMENT" = "prod" ]; then
    COMPOSE_FILE="docker-compose.yml"
    ENV_FILE=".env.production"
    echo "📦 使用生产环境配置"
else
    COMPOSE_FILE="docker-compose.dev.yml"
    ENV_FILE=".env.local"
    echo "🔧 使用开发环境配置"
fi

# 检查环境变量文件是否存在
if [ ! -f "$ENV_FILE" ]; then
    echo "❌ 环境变量文件 $ENV_FILE 不存在"
    echo "请复制 .env.example 并配置相应的环境变量"
    exit 1
fi

# 停止现有容器
echo "🛑 停止现有容器..."
docker compose -f $COMPOSE_FILE down

# 清理旧镜像 (可选)
read -p "是否清理旧的 Docker 镜像? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "🧹 清理旧镜像..."
    docker system prune -f
fi

# 构建并启动服务
if [ "$ENVIRONMENT" = "prod" ]; then
    echo "🏗️ 构建生产环境镜像..."
    docker compose -f $COMPOSE_FILE build --no-cache
    
    echo "🚀 启动生产环境服务..."
    docker compose -f $COMPOSE_FILE up -d
    
    # 等待数据库启动
    echo "⏳ 等待数据库启动..."
    sleep 10
    
    # 运行数据库迁移
    echo "📊 运行数据库迁移..."
    docker compose -f $COMPOSE_FILE exec web npx prisma migrate deploy
    
    # 运行数据库种子
    echo "🌱 运行数据库种子..."
    docker compose -f $COMPOSE_FILE exec web npx prisma db seed
else
    echo "🚀 启动开发环境服务..."
    docker compose -f $COMPOSE_FILE up -d
fi

# 检查服务状态
echo "📋 检查服务状态..."
docker compose -f $COMPOSE_FILE ps

# 显示日志
echo "📝 显示服务日志..."
docker compose -f $COMPOSE_FILE logs --tail=50

echo "✅ 部署完成!"

if [ "$ENVIRONMENT" = "prod" ]; then
    echo "🌐 生产环境访问地址: https://your-domain.com"
    echo "🔧 管理后台: https://your-domain.com/admin/login"
else
    echo "🌐 开发环境访问地址: http://localhost:3000"
    echo "🔧 管理后台: http://localhost:3000/admin/login"
fi

echo "📚 常用命令:"
echo "  查看日志: docker compose -f $COMPOSE_FILE logs -f"
echo "  停止服务: docker compose -f $COMPOSE_FILE down"
echo "  重启服务: docker compose -f $COMPOSE_FILE restart"
echo "  进入容器: docker compose -f $COMPOSE_FILE exec web sh"
