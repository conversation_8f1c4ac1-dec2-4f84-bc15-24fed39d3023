#!/bin/bash

# 对讲机软件下载站恢复脚本
# 从备份文件恢复数据库和文件

set -e

if [ $# -eq 0 ]; then
    echo "使用方法: $0 <backup_file>"
    echo "示例: $0 ./backups/downloads-website_backup_20240115_143022_complete.tar.gz"
    exit 1
fi

BACKUP_FILE=$1
PROJECT_NAME="downloads-website"
TEMP_DIR="/tmp/restore_$$"

if [ ! -f "$BACKUP_FILE" ]; then
    echo "❌ 备份文件不存在: $BACKUP_FILE"
    exit 1
fi

echo "🔄 开始恢复 $PROJECT_NAME..."
echo "📁 备份文件: $BACKUP_FILE"

# 创建临时目录
mkdir -p $TEMP_DIR

# 解压备份文件
echo "📦 解压备份文件..."
tar -xzf "$BACKUP_FILE" -C $TEMP_DIR

# 确认恢复操作
echo "⚠️ 警告: 此操作将覆盖现有数据!"
read -p "确定要继续恢复吗? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "❌ 恢复操作已取消"
    rm -rf $TEMP_DIR
    exit 1
fi

# 停止服务
echo "🛑 停止服务..."
docker compose down

# 恢复数据库
DATABASE_BACKUP=$(find $TEMP_DIR -name "*_database.sql" | head -1)
if [ -f "$DATABASE_BACKUP" ]; then
    echo "🗄️ 恢复数据库..."
    
    # 启动数据库服务
    docker compose up -d postgres
    sleep 10
    
    # 删除现有数据库并重新创建
    docker compose exec postgres psql -U postgres -c "DROP DATABASE IF EXISTS downloads_website;"
    docker compose exec postgres psql -U postgres -c "CREATE DATABASE downloads_website;"
    
    # 恢复数据库
    docker compose exec -T postgres psql -U postgres downloads_website < "$DATABASE_BACKUP"
    echo "✅ 数据库恢复完成"
else
    echo "⚠️ 未找到数据库备份文件"
fi

# 恢复上传文件
UPLOADS_BACKUP=$(find $TEMP_DIR -name "*_uploads.tar.gz" | head -1)
if [ -f "$UPLOADS_BACKUP" ]; then
    echo "📁 恢复上传文件..."
    
    # 备份现有文件
    if [ -d "./public/uploads" ]; then
        mv ./public/uploads ./public/uploads.backup.$(date +%Y%m%d_%H%M%S)
    fi
    
    # 恢复文件
    tar -xzf "$UPLOADS_BACKUP" -C ./public/
    echo "✅ 上传文件恢复完成"
else
    echo "⚠️ 未找到上传文件备份"
fi

# 恢复配置文件
CONFIG_BACKUP=$(find $TEMP_DIR -name "*_config.tar.gz" | head -1)
if [ -f "$CONFIG_BACKUP" ]; then
    echo "⚙️ 恢复配置文件..."
    
    # 备份现有配置
    for config in .env.production .env.local docker-compose.yml docker-compose.dev.yml; do
        if [ -f "$config" ]; then
            cp "$config" "${config}.backup.$(date +%Y%m%d_%H%M%S)"
        fi
    done
    
    # 恢复配置
    tar -xzf "$CONFIG_BACKUP" -C ./
    echo "✅ 配置文件恢复完成"
else
    echo "⚠️ 未找到配置文件备份"
fi

# 清理临时文件
rm -rf $TEMP_DIR

# 重新启动服务
echo "🚀 重新启动服务..."
docker compose up -d

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 15

# 检查服务状态
echo "📋 检查服务状态..."
docker compose ps

echo "✅ 恢复完成!"
echo "🌐 访问地址: http://localhost:3000"
echo "🔧 管理后台: http://localhost:3000/admin/login"

echo "📝 注意事项:"
echo "  - 请检查环境变量配置是否正确"
echo "  - 如有问题，可以使用备份的配置文件进行回滚"
echo "  - 建议重新生成应用密钥和密码"
