#!/bin/bash

# 对讲机软件下载站备份脚本
# 备份数据库和上传的文件

set -e

PROJECT_NAME="downloads-website"
BACKUP_DIR="./backups"
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_NAME="${PROJECT_NAME}_backup_${DATE}"

echo "📦 开始备份 $PROJECT_NAME..."

# 创建备份目录
mkdir -p $BACKUP_DIR

# 备份数据库
echo "🗄️ 备份数据库..."
docker compose exec postgres pg_dump -U postgres downloads_website > "$BACKUP_DIR/${BACKUP_NAME}_database.sql"

# 备份上传的文件
echo "📁 备份上传文件..."
if [ -d "./public/uploads" ]; then
    tar -czf "$BACKUP_DIR/${BACKUP_NAME}_uploads.tar.gz" -C ./public uploads
else
    echo "⚠️ 上传目录不存在，跳过文件备份"
fi

# 备份配置文件
echo "⚙️ 备份配置文件..."
tar -czf "$BACKUP_DIR/${BACKUP_NAME}_config.tar.gz" \
    .env.production \
    .env.local \
    docker-compose.yml \
    docker-compose.dev.yml \
    prisma/schema.prisma \
    2>/dev/null || echo "⚠️ 部分配置文件不存在"

# 创建完整备份包
echo "📦 创建完整备份包..."
cd $BACKUP_DIR
tar -czf "${BACKUP_NAME}_complete.tar.gz" \
    "${BACKUP_NAME}_database.sql" \
    "${BACKUP_NAME}_uploads.tar.gz" \
    "${BACKUP_NAME}_config.tar.gz" \
    2>/dev/null || echo "⚠️ 创建完整备份包时出现警告"

# 清理临时文件
rm -f "${BACKUP_NAME}_database.sql" "${BACKUP_NAME}_uploads.tar.gz" "${BACKUP_NAME}_config.tar.gz"

cd ..

echo "✅ 备份完成!"
echo "📁 备份文件: $BACKUP_DIR/${BACKUP_NAME}_complete.tar.gz"

# 清理旧备份 (保留最近7天)
echo "🧹 清理旧备份文件..."
find $BACKUP_DIR -name "${PROJECT_NAME}_backup_*.tar.gz" -mtime +7 -delete

echo "📊 当前备份文件:"
ls -lh $BACKUP_DIR/${PROJECT_NAME}_backup_*.tar.gz 2>/dev/null || echo "没有找到备份文件"
