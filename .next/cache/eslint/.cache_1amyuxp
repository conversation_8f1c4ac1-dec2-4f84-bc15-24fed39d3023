[{"/home/<USER>/src/app/about/page.tsx": "1", "/home/<USER>/src/app/admin/dashboard/page.tsx": "2", "/home/<USER>/src/app/admin/files/page.tsx": "3", "/home/<USER>/src/app/admin/login/page.tsx": "4", "/home/<USER>/src/app/api/categories/route.ts": "5", "/home/<USER>/src/app/api/files/[id]/download/route.ts": "6", "/home/<USER>/src/app/api/files/route.ts": "7", "/home/<USER>/src/app/api/llms/route.ts": "8", "/home/<USER>/src/app/api/robots/route.ts": "9", "/home/<USER>/src/app/api/sitemap/route.ts": "10", "/home/<USER>/src/app/categories/[slug]/page.tsx": "11", "/home/<USER>/src/app/categories/page.tsx": "12", "/home/<USER>/src/app/files/[id]/page.tsx": "13", "/home/<USER>/src/app/layout.tsx": "14", "/home/<USER>/src/app/page.tsx": "15", "/home/<USER>/src/app/search/page.tsx": "16", "/home/<USER>/src/components/FileUpload.tsx": "17", "/home/<USER>/src/components/admin/AdminLayout.tsx": "18", "/home/<USER>/src/components/ads/AdSense.tsx": "19", "/home/<USER>/src/components/ads/AdSenseScript.tsx": "20", "/home/<USER>/src/components/layout/Footer.tsx": "21", "/home/<USER>/src/components/layout/Header.tsx": "22", "/home/<USER>/src/components/seo/StructuredData.tsx": "23", "/home/<USER>/src/components/ui/Button.tsx": "24", "/home/<USER>/src/components/ui/Input.tsx": "25", "/home/<USER>/src/lib/prisma.ts": "26", "/home/<USER>/src/lib/utils.ts": "27", "/home/<USER>/src/types/index.ts": "28"}, {"size": 9524, "mtime": 1751562058096, "results": "29", "hashOfConfig": "30"}, {"size": 10443, "mtime": 1751562292070, "results": "31", "hashOfConfig": "30"}, {"size": 13891, "mtime": 1751562371429, "results": "32", "hashOfConfig": "30"}, {"size": 7128, "mtime": 1751693384330, "results": "33", "hashOfConfig": "30"}, {"size": 7418, "mtime": 1751562152290, "results": "34", "hashOfConfig": "30"}, {"size": 4755, "mtime": 1751693901263, "results": "35", "hashOfConfig": "30"}, {"size": 5782, "mtime": 1751562093263, "results": "36", "hashOfConfig": "30"}, {"size": 2428, "mtime": 1751598137349, "results": "37", "hashOfConfig": "30"}, {"size": 807, "mtime": 1751598114993, "results": "38", "hashOfConfig": "30"}, {"size": 2801, "mtime": 1751598102851, "results": "39", "hashOfConfig": "30"}, {"size": 9606, "mtime": 1751694044222, "results": "40", "hashOfConfig": "30"}, {"size": 7811, "mtime": 1751561871086, "results": "41", "hashOfConfig": "30"}, {"size": 14439, "mtime": 1751693985573, "results": "42", "hashOfConfig": "30"}, {"size": 2284, "mtime": 1751598246487, "results": "43", "hashOfConfig": "30"}, {"size": 5308, "mtime": 1751598288157, "results": "44", "hashOfConfig": "30"}, {"size": 13504, "mtime": 1751561962512, "results": "45", "hashOfConfig": "30"}, {"size": 10546, "mtime": 1751562211122, "results": "46", "hashOfConfig": "30"}, {"size": 5376, "mtime": 1751562317403, "results": "47", "hashOfConfig": "30"}, {"size": 2934, "mtime": 1751598214457, "results": "48", "hashOfConfig": "30"}, {"size": 421, "mtime": 1751598223637, "results": "49", "hashOfConfig": "30"}, {"size": 3573, "mtime": 1751561084970, "results": "50", "hashOfConfig": "30"}, {"size": 2659, "mtime": 1751561068022, "results": "51", "hashOfConfig": "30"}, {"size": 3987, "mtime": 1751598157942, "results": "52", "hashOfConfig": "30"}, {"size": 2188, "mtime": 1751561042968, "results": "53", "hashOfConfig": "30"}, {"size": 1380, "mtime": 1751561055064, "results": "54", "hashOfConfig": "30"}, {"size": 279, "mtime": 1751561491580, "results": "55", "hashOfConfig": "30"}, {"size": 3874, "mtime": 1751561029552, "results": "56", "hashOfConfig": "30"}, {"size": 2061, "mtime": 1751561008106, "results": "57", "hashOfConfig": "30"}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "3nrwru", {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/home/<USER>/src/app/about/page.tsx", [], [], "/home/<USER>/src/app/admin/dashboard/page.tsx", [], [], "/home/<USER>/src/app/admin/files/page.tsx", [], [], "/home/<USER>/src/app/admin/login/page.tsx", [], [], "/home/<USER>/src/app/api/categories/route.ts", [], [], "/home/<USER>/src/app/api/files/[id]/download/route.ts", [], [], "/home/<USER>/src/app/api/files/route.ts", [], [], "/home/<USER>/src/app/api/llms/route.ts", [], [], "/home/<USER>/src/app/api/robots/route.ts", [], [], "/home/<USER>/src/app/api/sitemap/route.ts", [], [], "/home/<USER>/src/app/categories/[slug]/page.tsx", [], [], "/home/<USER>/src/app/categories/page.tsx", [], [], "/home/<USER>/src/app/files/[id]/page.tsx", [], [], "/home/<USER>/src/app/layout.tsx", [], [], "/home/<USER>/src/app/page.tsx", [], [], "/home/<USER>/src/app/search/page.tsx", [], [], "/home/<USER>/src/components/FileUpload.tsx", [], [], "/home/<USER>/src/components/admin/AdminLayout.tsx", [], [], "/home/<USER>/src/components/ads/AdSense.tsx", [], [], "/home/<USER>/src/components/ads/AdSenseScript.tsx", [], [], "/home/<USER>/src/components/layout/Footer.tsx", [], [], "/home/<USER>/src/components/layout/Header.tsx", [], [], "/home/<USER>/src/components/seo/StructuredData.tsx", [], [], "/home/<USER>/src/components/ui/Button.tsx", [], [], "/home/<USER>/src/components/ui/Input.tsx", [], [], "/home/<USER>/src/lib/prisma.ts", [], [], "/home/<USER>/src/lib/utils.ts", [], [], "/home/<USER>/src/types/index.ts", [], []]