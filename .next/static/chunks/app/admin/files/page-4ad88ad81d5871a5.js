(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[138],{1482:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(9946).A)("Filter",[["polygon",{points:"22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3",key:"1yg77f"}]])},3009:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>f});var a=s(5155),i=s(2115),r=s(6874),l=s.n(r),d=s(9946);let c=(0,d.A)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]]);var n=s(4616),x=s(1482),o=s(8749),h=s(2657);let m=(0,d.A)("SquarePen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]]);var p=s(8835),u=s(3741),g=s(3915);let y=[{id:"1",title:"海能达 CPS 2.0 写频软件",description:"海能达数字对讲机专用写频软件",filename:"hytera-cps-2.0.zip",originalName:"Hytera_CPS_2.0_Setup.zip",size:0xf00000,downloadCount:1250,isActive:!0,category:"写频软件",version:"2.0.1",createdAt:"2024-01-15",updatedAt:"2024-01-15"},{id:"2",title:"摩托罗拉 CPS 16.0 写频软件",description:"摩托罗拉对讲机写频软件，兼容多个系列产品",filename:"motorola-cps-16.0.exe",originalName:"Motorola_CPS_16.0_Setup.exe",size:0x5555555,downloadCount:2100,isActive:!0,category:"写频软件",version:"16.0.3",createdAt:"2024-01-10",updatedAt:"2024-01-10"},{id:"3",title:"海能达 PD780 用户手册",description:"PD780数字对讲机详细使用说明",filename:"hytera-pd780-manual.pdf",originalName:"Hytera_PD780_Manual.pdf",size:5242880,downloadCount:650,isActive:!1,category:"产品手册",version:"1.0",createdAt:"2024-01-12",updatedAt:"2024-01-12"}];function f(){let[e,t]=(0,i.useState)(y),[s,r]=(0,i.useState)(""),[d,f]=(0,i.useState)(""),[j,v]=(0,i.useState)([]),[N,w]=(0,i.useState)(1),[b]=(0,i.useState)(10),k=e.filter(e=>{let t=e.title.toLowerCase().includes(s.toLowerCase())||e.description.toLowerCase().includes(s.toLowerCase()),a=!d||e.category===d;return t&&a}),A=Math.ceil(k.length/b),C=(N-1)*b,M=k.slice(C,C+b),S=e=>{v(t=>t.includes(e)?t.filter(t=>t!==e):[...t,e])},_=e=>{t(t=>t.map(t=>t.id===e?{...t,isActive:!t.isActive}:t))},z=e=>{confirm("确定要删除这个文件吗？")&&(t(t=>t.filter(t=>t.id!==e)),v(t=>t.filter(t=>t!==e)))};return(0,a.jsx)(p.A,{children:(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"文件管理"}),(0,a.jsx)("p",{className:"text-gray-600",children:"管理所有上传的文件"})]}),(0,a.jsxs)("div",{className:"flex space-x-3",children:[j.length>0&&(0,a.jsxs)(u.A,{variant:"danger",onClick:()=>{0!==j.length&&confirm("确定要删除选中的 ".concat(j.length," 个文件吗？"))&&(t(e=>e.filter(e=>!j.includes(e.id))),v([]))},children:[(0,a.jsx)(c,{className:"w-4 h-4 mr-2"}),"删除选中 (",j.length,")"]}),(0,a.jsx)(l(),{href:"/admin/files/upload",children:(0,a.jsxs)(u.A,{children:[(0,a.jsx)(n.A,{className:"w-4 h-4 mr-2"}),"上传文件"]})})]})]}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow-md border border-gray-200 p-6",children:(0,a.jsxs)("div",{className:"flex flex-col lg:flex-row gap-4",children:[(0,a.jsx)("div",{className:"flex-1",children:(0,a.jsx)(g.A,{placeholder:"搜索文件标题或描述...",value:s,onChange:e=>r(e.target.value)})}),(0,a.jsx)("div",{className:"w-full lg:w-48",children:(0,a.jsxs)("select",{value:d,onChange:e=>f(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500",children:[(0,a.jsx)("option",{value:"",children:"所有分类"}),(0,a.jsx)("option",{value:"写频软件",children:"写频软件"}),(0,a.jsx)("option",{value:"产品手册",children:"产品手册"}),(0,a.jsx)("option",{value:"技术资料",children:"技术资料"}),(0,a.jsx)("option",{value:"驱动程序",children:"驱动程序"})]})}),(0,a.jsxs)(u.A,{variant:"outline",children:[(0,a.jsx)(x.A,{className:"w-4 h-4 mr-2"}),"筛选"]})]})}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-md border border-gray-200",children:[(0,a.jsx)("div",{className:"p-6 border-b border-gray-200",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold text-gray-900",children:["文件列表 (",k.length,")"]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("input",{type:"checkbox",checked:j.length===M.length&&M.length>0,onChange:()=>{j.length===M.length?v([]):v(M.map(e=>e.id))},className:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"}),(0,a.jsx)("label",{className:"text-sm text-gray-700",children:"全选"})]})]})}),(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,a.jsx)("thead",{className:"bg-gray-50",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"选择"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"文件信息"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"分类"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"大小"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"下载量"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"状态"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"创建时间"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"操作"})]})}),(0,a.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:M.map(e=>(0,a.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("input",{type:"checkbox",checked:j.includes(e.id),onChange:()=>S(e.id),className:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"})}),(0,a.jsx)("td",{className:"px-6 py-4",children:(0,a.jsx)("div",{className:"flex items-center",children:(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.title}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:e.description}),e.version&&(0,a.jsxs)("div",{className:"text-xs text-gray-400",children:["v",e.version]})]})})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800",children:e.category})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:function(e){if(0===e)return"0 Bytes";let t=Math.floor(Math.log(e)/Math.log(1024));return parseFloat((e/Math.pow(1024,t)).toFixed(2))+" "+["Bytes","KB","MB","GB"][t]}(e.size)}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:e.downloadCount.toLocaleString()}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ".concat(e.isActive?"bg-green-100 text-green-800":"bg-red-100 text-red-800"),children:e.isActive?"启用":"禁用"})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:new Date(e.createdAt).toLocaleDateString("zh-CN")}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("button",{onClick:()=>_(e.id),className:"text-blue-600 hover:text-blue-900",title:e.isActive?"禁用":"启用",children:e.isActive?(0,a.jsx)(o.A,{className:"w-4 h-4"}):(0,a.jsx)(h.A,{className:"w-4 h-4"})}),(0,a.jsx)(l(),{href:"/admin/files/".concat(e.id,"/edit"),className:"text-indigo-600 hover:text-indigo-900",title:"编辑",children:(0,a.jsx)(m,{className:"w-4 h-4"})}),(0,a.jsx)("button",{onClick:()=>z(e.id),className:"text-red-600 hover:text-red-900",title:"删除",children:(0,a.jsx)(c,{className:"w-4 h-4"})})]})})]},e.id))})]})}),A>1&&(0,a.jsx)("div",{className:"px-6 py-4 border-t border-gray-200",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"text-sm text-gray-700",children:["显示 ",C+1," 到 ",Math.min(C+b,k.length)," 条， 共 ",k.length," 条记录"]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(u.A,{variant:"outline",size:"sm",onClick:()=>w(e=>Math.max(e-1,1)),disabled:1===N,children:"上一页"}),(0,a.jsxs)("span",{className:"text-sm text-gray-700",children:["第 ",N," 页，共 ",A," 页"]}),(0,a.jsx)(u.A,{variant:"outline",size:"sm",onClick:()=>w(e=>Math.min(e+1,A)),disabled:N===A,children:"下一页"})]})]})})]})]})})}},3915:(e,t,s)=>{"use strict";s.d(t,{A:()=>d});var a=s(5155),i=s(2115),r=s(9434);let l=i.forwardRef((e,t)=>{let{className:s,label:i,error:l,helperText:d,type:c="text",...n}=e;return(0,a.jsxs)("div",{className:"w-full",children:[i&&(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:i}),(0,a.jsx)("input",{type:c,className:(0,r.cn)("flex h-10 w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm ring-offset-white file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",l&&"border-red-500 focus-visible:ring-red-500",s),ref:t,...n}),l&&(0,a.jsx)("p",{className:"mt-1 text-sm text-red-600",children:l}),d&&!l&&(0,a.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:d})]})});l.displayName="Input";let d=l},7200:(e,t,s)=>{Promise.resolve().then(s.bind(s,3009))},8749:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(9946).A)("EyeOff",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])}},e=>{var t=t=>e(e.s=t);e.O(0,[620,144,441,684,358],()=>t(7200)),_N_E=e.O()}]);