(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[116],{2657:(e,r,s)=>{"use strict";s.d(r,{A:()=>t});let t=(0,s(9946).A)("Eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},3741:(e,r,s)=>{"use strict";s.d(r,{A:()=>n});var t=s(5155),a=s(2115),l=s(9434);let i=a.forwardRef((e,r)=>{let{className:s,variant:a="primary",size:i="md",loading:n=!1,disabled:c,children:d,...o}=e;return(0,t.jsxs)("button",{className:(0,l.cn)("inline-flex items-center justify-center rounded-md font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none",{primary:"bg-primary-600 text-white hover:bg-primary-700",secondary:"bg-secondary-200 text-secondary-900 hover:bg-secondary-300",outline:"border border-gray-300 bg-transparent hover:bg-gray-50",ghost:"hover:bg-gray-100",danger:"bg-red-600 text-white hover:bg-red-700"}[a],{sm:"h-8 px-3 text-sm",md:"h-10 px-4 py-2",lg:"h-12 px-6 text-lg"}[i],s),ref:r,disabled:c||n,...o,children:[n&&(0,t.jsxs)("svg",{className:"mr-2 h-4 w-4 animate-spin",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,t.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,t.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),d]})});i.displayName="Button";let n=i},4262:(e,r,s)=>{Promise.resolve().then(s.bind(s,7067))},5695:(e,r,s)=>{"use strict";var t=s(8999);s.o(t,"usePathname")&&s.d(r,{usePathname:function(){return t.usePathname}}),s.o(t,"useRouter")&&s.d(r,{useRouter:function(){return t.useRouter}})},7067:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>p});var t=s(5155),a=s(2115),l=s(5695),i=s(6874),n=s.n(i),c=s(1539),d=s(9946);let o=(0,d.A)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]]),m=(0,d.A)("Lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]]);var x=s(8749),h=s(2657),u=s(3741);function p(){let[e,r]=(0,a.useState)({email:"",password:""}),[s,i]=(0,a.useState)(!1),[d,p]=(0,a.useState)(!1),[y,b]=(0,a.useState)(""),f=(0,l.useRouter)(),g=async r=>{r.preventDefault(),p(!0),b("");try{let r=await fetch("/api/auth/login",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)}),s=await r.json();s.success?f.push("/admin/dashboard"):b(s.error||"登录失败")}catch(e){b("网络错误，请稍后重试")}finally{p(!1)}},v=e=>{let{name:s,value:t}=e.target;r(e=>({...e,[s]:t})),y&&b("")};return(0,t.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-primary-50 to-primary-100 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8",children:(0,t.jsxs)("div",{className:"max-w-md w-full space-y-8",children:[(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"flex items-center justify-center mb-6",children:(0,t.jsx)("div",{className:"bg-primary-600 p-3 rounded-full",children:(0,t.jsx)(c.A,{className:"w-8 h-8 text-white"})})}),(0,t.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-2",children:"管理员登录"}),(0,t.jsx)("p",{className:"text-gray-600",children:"登录到对讲机软件下载站管理后台"})]}),(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-md border border-gray-200 p-8",children:[(0,t.jsxs)("form",{onSubmit:g,className:"space-y-6",children:[y&&(0,t.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-md p-4",children:(0,t.jsx)("div",{className:"flex",children:(0,t.jsx)("div",{className:"text-sm text-red-700",children:y})})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-1",children:"邮箱地址"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,t.jsx)(o,{className:"h-5 w-5 text-gray-400"})}),(0,t.jsx)("input",{id:"email",name:"email",type:"email",autoComplete:"email",required:!0,value:e.email,onChange:v,className:"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent",placeholder:"输入管理员邮箱"})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700 mb-1",children:"密码"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,t.jsx)(m,{className:"h-5 w-5 text-gray-400"})}),(0,t.jsx)("input",{id:"password",name:"password",type:s?"text":"password",autoComplete:"current-password",required:!0,value:e.password,onChange:v,className:"block w-full pl-10 pr-10 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent",placeholder:"输入密码"}),(0,t.jsx)("button",{type:"button",className:"absolute inset-y-0 right-0 pr-3 flex items-center",onClick:()=>i(!s),children:s?(0,t.jsx)(x.A,{className:"h-5 w-5 text-gray-400 hover:text-gray-600"}):(0,t.jsx)(h.A,{className:"h-5 w-5 text-gray-400 hover:text-gray-600"})})]})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("input",{id:"remember-me",name:"remember-me",type:"checkbox",className:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"}),(0,t.jsx)("label",{htmlFor:"remember-me",className:"ml-2 block text-sm text-gray-700",children:"记住我"})]}),(0,t.jsx)("div",{className:"text-sm",children:(0,t.jsx)("button",{type:"button",onClick:()=>alert("请联系管理员重置密码"),className:"text-primary-600 hover:text-primary-500",children:"忘记密码？"})})]}),(0,t.jsx)(u.A,{type:"submit",className:"w-full",loading:d,disabled:!e.email||!e.password,children:"登录"})]}),(0,t.jsxs)("div",{className:"mt-6 p-4 bg-gray-50 rounded-md",children:[(0,t.jsx)("h4",{className:"text-sm font-medium text-gray-900 mb-2",children:"测试账号"}),(0,t.jsxs)("div",{className:"text-sm text-gray-600 space-y-1",children:[(0,t.jsx)("div",{children:"邮箱: <EMAIL>"}),(0,t.jsx)("div",{children:"密码: admin123"})]})]})]}),(0,t.jsx)("div",{className:"text-center",children:(0,t.jsx)(n(),{href:"/",className:"text-primary-600 hover:text-primary-500 text-sm font-medium",children:"← 返回首页"})})]})})}},8749:(e,r,s)=>{"use strict";s.d(r,{A:()=>t});let t=(0,s(9946).A)("EyeOff",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},9434:(e,r,s)=>{"use strict";s.d(r,{cn:()=>l});var t=s(2596),a=s(9688);function l(){for(var e=arguments.length,r=Array(e),s=0;s<e;s++)r[s]=arguments[s];return(0,a.QP)((0,t.$)(r))}}},e=>{var r=r=>e(e.s=r);e.O(0,[620,441,684,358],()=>r(4262)),_N_E=e.O()}]);