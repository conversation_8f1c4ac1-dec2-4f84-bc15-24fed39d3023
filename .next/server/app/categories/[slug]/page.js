"use strict";(()=>{var e={};e.id=93,e.ids=[93],e.modules={846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},3033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{e.exports=require("path")},4e3:(e,s,t)=>{t.r(s),t.d(s,{GlobalError:()=>n.a,__next_app__:()=>x,pages:()=>o,routeModule:()=>m,tree:()=>c});var r=t(5239),a=t(8088),i=t(8170),n=t.n(i),l=t(893),d={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>l[e]);t.d(s,d);let c={children:["",{children:["categories",{children:["[slug]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,7721)),"/home/<USER>/src/app/categories/[slug]/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,4431)),"/home/<USER>/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,5284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,o=["/home/<USER>/src/app/categories/[slug]/page.tsx"],x={require:t,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/categories/[slug]/page",pathname:"/categories/[slug]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},7721:(e,s,t)=>{t.r(s),t.d(s,{default:()=>N,generateMetadata:()=>v});var r=t(7413),a=t(4536),i=t.n(a),n=t(9916),l=t(3702),d=t(8804),c=t(5516),o=t(6373);let x=(0,o.A)("Filter",[["polygon",{points:"22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3",key:"1yg77f"}]]),m=(0,o.A)("Grid3x3",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M3 15h18",key:"5xshup"}],["path",{d:"M9 3v18",key:"fh3hqa"}],["path",{d:"M15 3v18",key:"14nvp0"}]]),h=(0,o.A)("List",[["path",{d:"M3 12h.01",key:"nlz23k"}],["path",{d:"M3 18h.01",key:"1tta3j"}],["path",{d:"M3 6h.01",key:"1rqtza"}],["path",{d:"M8 12h13",key:"1za7za"}],["path",{d:"M8 18h13",key:"1lx6n3"}],["path",{d:"M8 6h13",key:"ik3vkj"}]]);var p=t(7384);let j=(0,o.A)("Calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]]);var u=t(8926),g=t(4712),y=t(2496);let f=e=>({"programming-software":{id:"1",name:"写频软件",slug:"programming-software",description:"各品牌对讲机写频软件，支持频率设置、信道编程等功能",files:[{id:"1",title:"海能达 CPS 2.0 写频软件",description:"海能达数字对讲机专用写频软件，支持最新型号",filename:"hytera-cps-2.0.zip",size:0xf00000,downloadCount:1250,createdAt:"2024-01-15",version:"2.0.1",category:"海能达"},{id:"2",title:"摩托罗拉 CPS 16.0 写频软件",description:"摩托罗拉对讲机写频软件，兼容多个系列产品",filename:"motorola-cps-16.0.exe",size:0x5555555,downloadCount:2100,createdAt:"2024-01-10",version:"16.0.3",category:"摩托罗拉"},{id:"3",title:"建伍 KPG-D1N 写频软件",description:"建伍数字对讲机写频软件，支持DMR协议",filename:"kenwood-kpg-d1n.zip",size:0x1800000,downloadCount:890,createdAt:"2024-01-08",version:"3.2.1",category:"建伍"}]},manuals:{id:"2",name:"产品手册",slug:"manuals",description:"产品使用手册、技术手册和安装指南",files:[{id:"4",title:"海能达 PD780 用户手册",description:"PD780数字对讲机详细使用说明",filename:"hytera-pd780-manual.pdf",size:5242880,downloadCount:650,createdAt:"2024-01-12",version:"1.0",category:"用户手册"}]}})[e]||null;async function v({params:e}){let s=f((await e).slug);return s?{title:s.name,description:s.description,keywords:[s.name,"下载","对讲机","软件"]}:{title:"分类未找到"}}async function N({params:e}){let s=f((await e).slug);return s||(0,n.notFound)(),(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,r.jsx)(u.default,{}),(0,r.jsxs)("main",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,r.jsx)("nav",{className:"flex mb-8","aria-label":"Breadcrumb",children:(0,r.jsxs)("ol",{className:"inline-flex items-center space-x-1 md:space-x-3",children:[(0,r.jsx)("li",{className:"inline-flex items-center",children:(0,r.jsx)(i(),{href:"/",className:"text-gray-700 hover:text-primary-600",children:"首页"})}),(0,r.jsx)("li",{children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(l.A,{className:"w-4 h-4 text-gray-400 mx-1"}),(0,r.jsx)(i(),{href:"/categories",className:"text-gray-700 hover:text-primary-600",children:"分类浏览"})]})}),(0,r.jsx)("li",{children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(l.A,{className:"w-4 h-4 text-gray-400 mx-1"}),(0,r.jsx)("span",{className:"text-gray-500",children:s.name})]})})]})}),(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-4",children:s.name}),(0,r.jsx)("p",{className:"text-lg text-gray-600 mb-6",children:s.description}),(0,r.jsxs)("div",{className:"flex items-center space-x-6 text-sm text-gray-500",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(d.A,{className:"w-4 h-4 mr-1"}),(0,r.jsxs)("span",{children:[s.files.length," 个文件"]})]}),(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(c.A,{className:"w-4 h-4 mr-1"}),(0,r.jsxs)("span",{children:[s.files.reduce((e,s)=>e+s.downloadCount,0)," 次下载"]})]})]})]}),(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 space-y-4 sm:space-y-0",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)(y.A,{variant:"outline",size:"sm",children:[(0,r.jsx)(x,{className:"w-4 h-4 mr-2"}),"筛选"]}),(0,r.jsxs)("select",{className:"px-3 py-2 border border-gray-300 rounded-md text-sm",children:[(0,r.jsx)("option",{children:"按下载量排序"}),(0,r.jsx)("option",{children:"按时间排序"}),(0,r.jsx)("option",{children:"按文件大小排序"})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(y.A,{variant:"ghost",size:"sm",children:(0,r.jsx)(m,{className:"w-4 h-4"})}),(0,r.jsx)(y.A,{variant:"ghost",size:"sm",children:(0,r.jsx)(h,{className:"w-4 h-4"})})]})]}),(0,r.jsx)("div",{className:"space-y-4",children:s.files.map(e=>(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-md border border-gray-200 p-6",children:(0,r.jsxs)("div",{className:"flex flex-col lg:flex-row lg:items-center lg:justify-between",children:[(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsxs)("div",{className:"flex items-start justify-between mb-2",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:(0,r.jsx)(i(),{href:`/files/${e.id}`,className:"hover:text-primary-600 transition-colors",children:e.title})}),(0,r.jsx)("span",{className:"bg-primary-100 text-primary-800 text-xs font-medium px-2.5 py-0.5 rounded",children:e.category})]}),(0,r.jsx)("p",{className:"text-gray-600 mb-4",children:e.description}),(0,r.jsxs)("div",{className:"flex flex-wrap items-center gap-4 text-sm text-gray-500",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(p.A,{className:"w-4 h-4 mr-1"}),(0,r.jsx)("span",{children:function(e){if(0===e)return"0 Bytes";let s=Math.floor(Math.log(e)/Math.log(1024));return parseFloat((e/Math.pow(1024,s)).toFixed(2))+" "+["Bytes","KB","MB","GB"][s]}(e.size)})]}),(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(c.A,{className:"w-4 h-4 mr-1"}),(0,r.jsxs)("span",{children:[e.downloadCount," 次下载"]})]}),(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(j,{className:"w-4 h-4 mr-1"}),(0,r.jsx)("span",{children:new Date(e.createdAt).toLocaleDateString("zh-CN")})]}),e.version&&(0,r.jsxs)("span",{className:"bg-gray-100 text-gray-800 text-xs px-2 py-1 rounded",children:["v",e.version]})]})]}),(0,r.jsx)("div",{className:"mt-4 lg:mt-0 lg:ml-6",children:(0,r.jsx)(i(),{href:`/files/${e.id}`,children:(0,r.jsxs)(y.A,{children:[(0,r.jsx)(c.A,{className:"w-4 h-4 mr-2"}),"下载"]})})})]})},e.id))}),(0,r.jsx)("div",{className:"mt-8 flex justify-center",children:(0,r.jsxs)("nav",{className:"flex items-center space-x-2",children:[(0,r.jsx)(y.A,{variant:"outline",size:"sm",disabled:!0,children:"上一页"}),(0,r.jsx)(y.A,{variant:"outline",size:"sm",className:"bg-primary-600 text-white",children:"1"}),(0,r.jsx)(y.A,{variant:"outline",size:"sm",children:"2"}),(0,r.jsx)(y.A,{variant:"outline",size:"sm",children:"3"}),(0,r.jsx)(y.A,{variant:"outline",size:"sm",children:"下一页"})]})})]}),(0,r.jsx)(g.A,{})]})}},9121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var s=require("../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[447,424,43,593,790,446],()=>t(4e3));module.exports=r})();