(()=>{var e={};e.id=490,e.ids=[490],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},4094:(e,s,t)=>{"use strict";t.r(s),t.d(s,{patchFetch:()=>y,routeModule:()=>f,serverHooks:()=>h,workAsyncStorage:()=>w,workUnitAsyncStorage:()=>g});var r={};t.r(r),t.d(r,{GET:()=>m,POST:()=>x});var o=t(6559),n=t(8088),i=t(7719),a=t(2190),u=t(9748),p=t(9021),c=t(3873),d=t.n(c);let l=e=>({1:{id:"1",title:"海能达 CPS 2.0 写频软件",filename:"hytera-cps-2.0.zip",originalName:"Hytera_CPS_2.0_Setup.zip",mimeType:"application/zip",size:0xf00000,downloadCount:1250,isActive:!0},2:{id:"2",title:"摩托罗拉 CPS 16.0 写频软件",filename:"motorola-cps-16.0.exe",originalName:"Motorola_CPS_16.0_Setup.exe",mimeType:"application/x-msdownload",size:0x5555555,downloadCount:2100,isActive:!0}})[e]||null;async function m(e,s){try{let t=(await s.params).id,r=l(t);if(!r)return a.NextResponse.json({success:!1,error:"文件不存在"},{status:404});if(!r.isActive)return a.NextResponse.json({success:!1,error:"文件已被禁用"},{status:403});let o=d().join(process.cwd(),"public","uploads",r.filename);if(!(0,p.existsSync)(o))return a.NextResponse.json({success:!1,error:"文件不存在于服务器"},{status:404});try{let s=await (0,u.readFile)(o),n=e.headers.get("user-agent")||"",i=e.headers.get("x-forwarded-for")||e.headers.get("x-real-ip")||"unknown";console.log("文件下载:",{fileId:t,filename:r.filename,clientIP:i,userAgent:n,timestamp:new Date().toISOString()});let p=new Headers;return p.set("Content-Type",r.mimeType),p.set("Content-Length",r.size.toString()),p.set("Content-Disposition",`attachment; filename="${encodeURIComponent(r.originalName)}"`),p.set("Cache-Control","no-cache, no-store, must-revalidate"),p.set("Pragma","no-cache"),p.set("Expires","0"),new a.NextResponse(s,{status:200,headers:p})}catch(e){return console.error("读取文件失败:",e),a.NextResponse.json({success:!1,error:"文件读取失败"},{status:500})}}catch(e){return console.error("下载处理失败:",e),a.NextResponse.json({success:!1,error:"下载处理失败"},{status:500})}}async function x(e,s){try{let e=(await s.params).id,t=l(e);if(!t)return a.NextResponse.json({success:!1,error:"文件不存在"},{status:404});if(!t.isActive)return a.NextResponse.json({success:!1,error:"文件已被禁用"},{status:403});let r=d().join(process.cwd(),"public","uploads",t.filename);if(!(0,p.existsSync)(r))return a.NextResponse.json({success:!1,error:"文件不存在于服务器"},{status:404});return a.NextResponse.json({success:!0,data:{id:t.id,title:t.title,filename:t.originalName,size:t.size,mimeType:t.mimeType,downloadCount:t.downloadCount,downloadUrl:`/api/files/${e}/download`}})}catch(e){return console.error("获取下载信息失败:",e),a.NextResponse.json({success:!1,error:"获取下载信息失败"},{status:500})}}let f=new o.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/files/[id]/download/route",pathname:"/api/files/[id]/download",filename:"route",bundlePath:"app/api/files/[id]/download/route"},resolvedPagePath:"/home/<USER>/src/app/api/files/[id]/download/route.ts",nextConfigOutput:"standalone",userland:r}),{workAsyncStorage:w,workUnitAsyncStorage:g,serverHooks:h}=f;function y(){return(0,i.patchFetch)({workAsyncStorage:w,workUnitAsyncStorage:g})}},4870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},6487:()=>{},8335:()=>{},9021:e=>{"use strict";e.exports=require("fs")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9748:e=>{"use strict";e.exports=require("fs/promises")}};var s=require("../../../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[447,580],()=>t(4094));module.exports=r})();