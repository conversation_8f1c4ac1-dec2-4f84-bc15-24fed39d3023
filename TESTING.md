# 测试指南

本文档描述了对讲机软件下载站的测试策略和测试用例。

## 🧪 测试策略

### 1. 功能测试

#### 前台功能测试
- [ ] 首页加载和显示
- [ ] 分类浏览功能
- [ ] 搜索功能
- [ ] 文件详情页显示
- [ ] 文件下载功能
- [ ] 响应式设计测试

#### 后台功能测试
- [ ] 管理员登录
- [ ] 文件上传功能
- [ ] 文件管理功能
- [ ] 分类管理功能
- [ ] 用户管理功能

### 2. API测试

#### 文件相关API
```bash
# 获取文件列表
curl -X GET "http://localhost:3000/api/files"

# 上传文件 (需要multipart/form-data)
curl -X POST "http://localhost:3000/api/files" \
  -F "file=@test-file.zip" \
  -F "title=测试文件" \
  -F "categoryId=programming-software"

# 下载文件
curl -X GET "http://localhost:3000/api/files/1/download"
```

#### 分类相关API
```bash
# 获取分类列表
curl -X GET "http://localhost:3000/api/categories"

# 创建分类
curl -X POST "http://localhost:3000/api/categories" \
  -H "Content-Type: application/json" \
  -d '{"name":"新分类","description":"分类描述"}'
```

### 3. SEO测试

#### 检查SEO元素
```bash
# 检查sitemap
curl -X GET "http://localhost:3000/sitemap.xml"

# 检查robots.txt
curl -X GET "http://localhost:3000/robots.txt"

# 检查llms.txt
curl -X GET "http://localhost:3000/llms.txt"
```

#### 结构化数据验证
使用Google的结构化数据测试工具验证：
- https://search.google.com/test/rich-results

### 4. 性能测试

#### 页面加载速度测试
```bash
# 使用curl测试响应时间
curl -w "@curl-format.txt" -o /dev/null -s "http://localhost:3000"
```

创建 `curl-format.txt` 文件：
```
     time_namelookup:  %{time_namelookup}\n
        time_connect:  %{time_connect}\n
     time_appconnect:  %{time_appconnect}\n
    time_pretransfer:  %{time_pretransfer}\n
       time_redirect:  %{time_redirect}\n
  time_starttransfer:  %{time_starttransfer}\n
                     ----------\n
          time_total:  %{time_total}\n
```

#### 并发测试
```bash
# 使用ab进行并发测试
ab -n 100 -c 10 http://localhost:3000/
```

### 5. 安全测试

#### 文件上传安全测试
- [ ] 上传恶意文件类型
- [ ] 上传超大文件
- [ ] 上传包含脚本的文件
- [ ] 路径遍历攻击测试

#### SQL注入测试
- [ ] 在搜索框中输入SQL注入代码
- [ ] 在API参数中测试SQL注入

#### XSS测试
- [ ] 在表单中输入XSS代码
- [ ] 在URL参数中测试XSS

## 🔧 测试环境设置

### 1. 启动测试环境
```bash
# 启动开发环境
npm run dev

# 或启动Docker环境
docker compose -f docker-compose.dev.yml up -d
```

### 2. 准备测试数据
```bash
# 运行数据库种子
npx prisma db seed
```

### 3. 测试用户账号
- 管理员: <EMAIL> / admin123

## 📋 测试用例

### 用例1: 文件上传测试
**目标**: 验证文件上传功能正常工作
**步骤**:
1. 登录管理后台
2. 进入文件上传页面
3. 选择测试文件
4. 填写文件信息
5. 点击上传
**期望结果**: 文件成功上传并显示在文件列表中

### 用例2: 文件下载测试
**目标**: 验证文件下载功能正常工作
**步骤**:
1. 访问文件详情页
2. 点击下载按钮
3. 检查下载的文件
**期望结果**: 文件正确下载且内容完整

### 用例3: 搜索功能测试
**目标**: 验证搜索功能正常工作
**步骤**:
1. 访问搜索页面
2. 输入搜索关键词
3. 点击搜索
4. 查看搜索结果
**期望结果**: 返回相关的搜索结果

### 用例4: 响应式设计测试
**目标**: 验证网站在不同设备上正常显示
**步骤**:
1. 在桌面浏览器中访问网站
2. 调整浏览器窗口大小
3. 使用移动设备访问网站
**期望结果**: 网站在不同屏幕尺寸下正常显示

## 🐛 常见问题排查

### 1. 文件上传失败
- 检查文件大小是否超过限制
- 检查文件类型是否被允许
- 检查上传目录权限
- 查看服务器日志

### 2. 数据库连接失败
- 检查数据库服务是否启动
- 检查数据库连接字符串
- 检查数据库用户权限

### 3. 页面加载缓慢
- 检查数据库查询性能
- 检查静态资源加载
- 检查网络连接

## 📊 性能基准

### 页面加载时间目标
- 首页: < 2秒
- 分类页: < 3秒
- 搜索页: < 2秒
- 文件详情页: < 2秒

### 并发处理能力目标
- 同时在线用户: 100+
- 并发下载: 50+
- API响应时间: < 500ms

## 🔄 持续测试

### 自动化测试
建议集成以下自动化测试：
- 单元测试 (Jest)
- 集成测试 (Cypress)
- API测试 (Postman/Newman)
- 性能测试 (Lighthouse CI)

### 监控指标
- 页面加载时间
- API响应时间
- 错误率
- 用户活跃度
- 下载成功率

---

定期执行这些测试用例，确保网站功能正常运行和用户体验良好。
