services:
  # PostgreSQL 数据库
  postgres:
    image: postgres:15-alpine
    container_name: downloads_website_db
    restart: unless-stopped
    environment:
      POSTGRES_DB: downloads_website
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - downloads_network

  # Redis (用于缓存和会话)
  redis:
    image: redis:7-alpine
    container_name: downloads_website_redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - downloads_network

  # Web 应用 (生产环境)
  web:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: downloads_website_web
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - DATABASE_URL=********************************************/downloads_website
      - NEXT_PUBLIC_BASE_URL=http://localhost:3000
    depends_on:
      - postgres
      - redis
    volumes:
      - ./public/uploads:/app/public/uploads
    networks:
      - downloads_network

volumes:
  postgres_data:
  redis_data:

networks:
  downloads_network:
    driver: bridge
