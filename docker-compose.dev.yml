services:
  # PostgreSQL 数据库 (开发环境)
  postgres:
    image: postgres:15-alpine
    container_name: downloads_website_db_dev
    restart: unless-stopped
    environment:
      POSTGRES_DB: downloads_website
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    ports:
      - "5432:5432"
    volumes:
      - postgres_dev_data:/var/lib/postgresql/data
    networks:
      - downloads_dev_network

  # Redis (开发环境)
  redis:
    image: redis:7-alpine
    container_name: downloads_website_redis_dev
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_dev_data:/data
    networks:
      - downloads_dev_network

volumes:
  postgres_dev_data:
  redis_dev_data:

networks:
  downloads_dev_network:
    driver: bridge
