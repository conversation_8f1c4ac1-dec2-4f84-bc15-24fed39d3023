# 对讲机软件下载站

专业的对讲机行业软件下载平台，提供各品牌写频软件、产品资料、技术文档等文件下载服务。

## 🚀 项目特色

- **专业性**: 专注对讲机行业，内容专业可靠
- **安全性**: 所有文件经过安全检测，无病毒无恶意代码  
- **SEO优化**: 完整的SEO技术实现，包括sitemap、结构化数据等
- **响应式设计**: 支持桌面端和移动端访问
- **管理后台**: 完整的文件管理和内容管理系统
- **广告集成**: 预留Google AdSense广告位
- **Docker部署**: 支持容器化部署

## 🛠️ 技术栈

- **前端**: Next.js 15 + TypeScript + Tailwind CSS
- **后端**: Next.js API Routes
- **数据库**: PostgreSQL + Prisma ORM
- **部署**: Docker + Docker Compose
- **SEO**: 结构化数据 + sitemap + robots.txt + llms.txt

## 📁 项目结构

```
├── src/
│   ├── app/                    # Next.js App Router页面
│   │   ├── admin/             # 管理后台
│   │   ├── categories/        # 分类页面
│   │   ├── files/            # 文件详情页
│   │   ├── search/           # 搜索页面
│   │   └── api/              # API路由
│   ├── components/            # 可复用组件
│   │   ├── ui/               # 基础UI组件
│   │   ├── layout/           # 布局组件
│   │   ├── admin/            # 管理后台组件
│   │   ├── ads/              # 广告组件
│   │   └── seo/              # SEO组件
│   ├── lib/                  # 工具库
│   └── types/                # TypeScript类型定义
├── prisma/                   # 数据库模型和迁移
├── scripts/                  # 部署和维护脚本
└── public/                   # 静态资源
```

## 🚀 快速开始

### 开发环境

1. **克隆项目**
   ```bash
   git clone <repository-url>
   cd downloads-website
   ```

2. **安装依赖**
   ```bash
   npm install
   ```

3. **配置环境变量**
   ```bash
   cp .env.example .env.local
   # 编辑 .env.local 配置数据库等信息
   ```

4. **启动数据库**
   ```bash
   docker compose -f docker-compose.dev.yml up -d
   ```

5. **初始化数据库**
   ```bash
   npx prisma migrate dev
   npx prisma db seed
   ```

6. **启动开发服务器**
   ```bash
   npm run dev
   ```

7. **访问应用**
   - 前台: http://localhost:3000
   - 管理后台: http://localhost:3000/admin/login

### 生产环境部署

1. **配置生产环境变量**
   ```bash
   cp .env.example .env.production
   # 编辑 .env.production 配置生产环境信息
   ```

2. **使用部署脚本**
   ```bash
   ./scripts/deploy.sh prod
   ```

## 📋 功能清单

### ✅ 已完成功能

- [x] 项目架构设计与初始化
- [x] 数据库设计与配置
- [x] 前端页面结构开发
  - [x] 首页
  - [x] 分类浏览页
  - [x] 文件详情页
  - [x] 搜索页面
  - [x] 关于我们页
- [x] 文件管理系统
  - [x] 文件上传API
  - [x] 文件下载API
  - [x] 分类管理API
- [x] 后台管理系统
  - [x] 管理员登录
  - [x] 仪表板
  - [x] 文件管理
  - [x] 分类管理
- [x] SEO优化实现
  - [x] Sitemap生成
  - [x] Robots.txt
  - [x] LLMs.txt
  - [x] 结构化数据
  - [x] Meta标签优化
- [x] 广告位预留与集成
- [x] Docker化与部署配置
- [x] 测试与优化

## 🔧 管理后台

### 默认管理员账号
- 邮箱: <EMAIL>
- 密码: admin123

### 主要功能
- 文件上传和管理
- 分类管理
- 用户管理
- 下载统计
- 系统设置

## 📊 SEO优化

### 已实现的SEO技术
- ✅ 结构化数据 (JSON-LD)
- ✅ 动态sitemap生成
- ✅ Robots.txt配置
- ✅ LLMs.txt (AI模型友好)
- ✅ Open Graph标签
- ✅ 语义化URL结构
- ✅ 面包屑导航
- ✅ 响应式设计

## 💰 广告集成

项目已预留Google AdSense广告位：
- 顶部横幅广告
- 内容中间广告
- 侧边栏广告
- 底部广告

配置方法：
1. 在 `.env.production` 中设置 `NEXT_PUBLIC_ADSENSE_CLIENT_ID`
2. 广告位会自动显示

## 🐳 Docker部署

### 开发环境
```bash
docker compose -f docker-compose.dev.yml up -d
```

### 生产环境
```bash
docker compose up -d
```

### 常用命令
```bash
# 查看日志
docker compose logs -f

# 进入容器
docker compose exec web sh

# 备份数据
./scripts/backup.sh

# 恢复数据
./scripts/restore.sh backup_file.tar.gz
```

## 🔒 安全考虑

- 文件类型验证
- 文件大小限制
- 管理员权限控制
- SQL注入防护
- XSS防护
- CSRF防护

## 📈 性能优化

- Next.js静态生成
- 图片优化
- 代码分割
- 缓存策略
- CDN支持

## 🤝 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建Pull Request

## 📄 许可证

MIT License

## 📞 联系我们

如有问题或建议，请通过以下方式联系：
- 邮箱: <EMAIL>
- 网站: https://your-domain.com

---

© 2024 对讲机软件下载站. All rights reserved.
