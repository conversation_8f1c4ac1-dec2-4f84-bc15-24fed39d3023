import Link from 'next/link'
import { Download, FileText, Shield, Search, TrendingUp } from 'lucide-react'
import Header from '@/components/layout/Header'
import Footer from '@/components/layout/Footer'
import Button from '@/components/ui/Button'
import { HeaderAd, ContentAd, FooterAd } from '@/components/ads/AdSense'

export default function HomePage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      <Header />

      {/* 顶部广告位 */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-4">
        <HeaderAd />
      </div>

      {/* Hero Section */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
            专业的对讲机软件
            <span className="text-primary-600">下载平台</span>
          </h2>
          <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
            提供各品牌写频软件、产品资料、技术文档等专业文件下载服务，支持多种格式，安全可靠。
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/categories">
              <Button size="lg" className="text-lg px-8 py-3">
                <Download className="w-5 h-5 mr-2" />
                开始下载
              </Button>
            </Link>
            <Link href="/search">
              <Button variant="secondary" size="lg" className="text-lg px-8 py-3">
                <Search className="w-5 h-5 mr-2" />
                搜索文件
              </Button>
            </Link>
          </div>
        </div>
      </section>

      {/* 内容中间广告位 */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <ContentAd />
      </div>

      {/* Features Section */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h3 className="text-3xl font-bold text-gray-900 mb-4">为什么选择我们</h3>
            <p className="text-lg text-gray-600">专业、安全、便捷的文件下载体验</p>
          </div>
          
          <div className="grid md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="bg-primary-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                <FileText className="w-8 h-8 text-primary-600" />
              </div>
              <h4 className="text-xl font-semibold text-gray-900 mb-2">丰富的资源</h4>
              <p className="text-gray-600">涵盖各大品牌的写频软件、产品手册、技术资料等专业文档</p>
            </div>
            
            <div className="text-center">
              <div className="bg-primary-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                <Shield className="w-8 h-8 text-primary-600" />
              </div>
              <h4 className="text-xl font-semibold text-gray-900 mb-2">安全可靠</h4>
              <p className="text-gray-600">所有文件经过安全检测，确保无病毒无恶意代码，放心下载</p>
            </div>
            
            <div className="text-center">
              <div className="bg-primary-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                <TrendingUp className="w-8 h-8 text-primary-600" />
              </div>
              <h4 className="text-xl font-semibold text-gray-900 mb-2">持续更新</h4>
              <p className="text-gray-600">定期更新最新版本软件和资料，保持内容的时效性</p>
            </div>
          </div>
        </div>
      </section>

      {/* Popular Categories */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h3 className="text-3xl font-bold text-gray-900 mb-4">热门分类</h3>
            <p className="text-lg text-gray-600">快速找到您需要的文件类型</p>
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            {[
              { name: '写频软件', count: '120+', href: '/categories/programming-software' },
              { name: '产品手册', count: '80+', href: '/categories/manuals' },
              { name: '技术资料', count: '150+', href: '/categories/technical-docs' },
              { name: '驱动程序', count: '60+', href: '/categories/drivers' },
            ].map((category) => (
              <Link key={category.name} href={category.href} className="bg-white rounded-lg shadow-md border border-gray-200 p-6 hover:shadow-lg transition-shadow">
                <h4 className="text-lg font-semibold text-gray-900 mb-2">{category.name}</h4>
                <p className="text-gray-600">{category.count} 个文件</p>
              </Link>
            ))}
          </div>
        </div>
      </section>

      {/* 底部广告位 */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <FooterAd />
      </div>

      <Footer />
    </div>
  )
}
