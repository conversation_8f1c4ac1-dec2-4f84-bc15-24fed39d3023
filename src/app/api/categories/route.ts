import { NextRequest, NextResponse } from 'next/server'
import { nanoid } from 'nanoid'

// 模拟分类数据 - 实际项目中会从数据库获取
const mockCategories = [
  {
    id: '1',
    name: '写频软件',
    slug: 'programming-software',
    description: '各品牌对讲机写频软件',
    parentId: null,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
    children: [
      {
        id: '11',
        name: '海能达',
        slug: 'hytera',
        description: '海能达对讲机写频软件',
        parentId: '1',
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T00:00:00Z'
      },
      {
        id: '12',
        name: '摩托罗拉',
        slug: 'motorola',
        description: '摩托罗拉对讲机写频软件',
        parentId: '1',
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T00:00:00Z'
      }
    ]
  },
  {
    id: '2',
    name: '产品手册',
    slug: 'manuals',
    description: '产品使用手册和说明书',
    parentId: null,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
    children: []
  }
]

function generateSlug(name: string): string {
  return name
    .toLowerCase()
    .trim()
    .replace(/[\s\W-]+/g, '-')
    .replace(/^-+|-+$/g, '')
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const includeChildren = searchParams.get('includeChildren') === 'true'
    const parentId = searchParams.get('parentId')

    let categories = mockCategories

    // 如果指定了 parentId，只返回该父分类的子分类
    if (parentId) {
      const parentCategory = categories.find(cat => cat.id === parentId)
      categories = parentCategory ? parentCategory.children : []
    } else if (!includeChildren) {
      // 如果不包含子分类，移除 children 字段
      categories = categories.map(cat => {
        const { children, ...categoryWithoutChildren } = cat
        return categoryWithoutChildren
      })
    }

    return NextResponse.json({
      success: true,
      data: categories
    })
  } catch (error) {
    console.error('获取分类列表失败:', error)
    return NextResponse.json(
      { success: false, error: '获取分类列表失败' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { name, description, parentId } = body

    // 验证必填字段
    if (!name) {
      return NextResponse.json(
        { success: false, error: '分类名称不能为空' },
        { status: 400 }
      )
    }

    // 生成 slug
    const slug = generateSlug(name)

    // 检查 slug 是否已存在
    const existingCategory = mockCategories.find(cat => cat.slug === slug)
    if (existingCategory) {
      return NextResponse.json(
        { success: false, error: '分类名称已存在' },
        { status: 400 }
      )
    }

    // 如果有父分类，验证父分类是否存在
    if (parentId) {
      const parentCategory = mockCategories.find(cat => cat.id === parentId)
      if (!parentCategory) {
        return NextResponse.json(
          { success: false, error: '父分类不存在' },
          { status: 400 }
        )
      }
    }

    // 创建新分类
    const newCategory = {
      id: nanoid(),
      name,
      slug,
      description: description || '',
      parentId: parentId || null,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      children: []
    }

    // 这里应该保存到数据库
    console.log('创建新分类:', newCategory)

    return NextResponse.json({
      success: true,
      data: newCategory,
      message: '分类创建成功'
    })
  } catch (error) {
    console.error('创建分类失败:', error)
    return NextResponse.json(
      { success: false, error: '创建分类失败' },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const { id, name, description, parentId } = body

    // 验证必填字段
    if (!id || !name) {
      return NextResponse.json(
        { success: false, error: '分类ID和名称不能为空' },
        { status: 400 }
      )
    }

    // 查找要更新的分类
    const categoryIndex = mockCategories.findIndex(cat => cat.id === id)
    if (categoryIndex === -1) {
      return NextResponse.json(
        { success: false, error: '分类不存在' },
        { status: 404 }
      )
    }

    // 生成新的 slug
    const slug = generateSlug(name)

    // 检查新的 slug 是否与其他分类冲突
    const existingCategory = mockCategories.find(cat => cat.slug === slug && cat.id !== id)
    if (existingCategory) {
      return NextResponse.json(
        { success: false, error: '分类名称已存在' },
        { status: 400 }
      )
    }

    // 如果有父分类，验证父分类是否存在且不是自己
    if (parentId) {
      if (parentId === id) {
        return NextResponse.json(
          { success: false, error: '不能将分类设置为自己的子分类' },
          { status: 400 }
        )
      }
      
      const parentCategory = mockCategories.find(cat => cat.id === parentId)
      if (!parentCategory) {
        return NextResponse.json(
          { success: false, error: '父分类不存在' },
          { status: 400 }
        )
      }
    }

    // 更新分类
    const updatedCategory = {
      ...mockCategories[categoryIndex],
      name,
      slug,
      description: description || '',
      parentId: parentId || null,
      updatedAt: new Date().toISOString()
    }

    // 这里应该更新数据库
    console.log('更新分类:', updatedCategory)

    return NextResponse.json({
      success: true,
      data: updatedCategory,
      message: '分类更新成功'
    })
  } catch (error) {
    console.error('更新分类失败:', error)
    return NextResponse.json(
      { success: false, error: '更新分类失败' },
      { status: 500 }
    )
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')

    if (!id) {
      return NextResponse.json(
        { success: false, error: '分类ID不能为空' },
        { status: 400 }
      )
    }

    // 查找要删除的分类
    const category = mockCategories.find(cat => cat.id === id)
    if (!category) {
      return NextResponse.json(
        { success: false, error: '分类不存在' },
        { status: 404 }
      )
    }

    // 检查是否有子分类
    if (category.children && category.children.length > 0) {
      return NextResponse.json(
        { success: false, error: '该分类下还有子分类，无法删除' },
        { status: 400 }
      )
    }

    // 这里应该检查是否有文件使用该分类
    // 如果有文件使用该分类，应该禁止删除或提供转移选项

    // 这里应该从数据库删除
    console.log('删除分类:', id)

    return NextResponse.json({
      success: true,
      message: '分类删除成功'
    })
  } catch (error) {
    console.error('删除分类失败:', error)
    return NextResponse.json(
      { success: false, error: '删除分类失败' },
      { status: 500 }
    )
  }
}
