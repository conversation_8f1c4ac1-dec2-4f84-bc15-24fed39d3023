import { NextRequest, NextResponse } from 'next/server'
import { writeFile, mkdir } from 'fs/promises'
import { existsSync } from 'fs'
import path from 'path'
import { nanoid } from 'nanoid'

// 允许的文件类型
const ALLOWED_FILE_TYPES = [
  'application/zip',
  'application/x-rar-compressed',
  'application/x-7z-compressed',
  'application/pdf',
  'application/msword',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  'application/vnd.ms-excel',
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  'application/x-msdownload',
  'application/x-msdos-program',
  'text/plain',
  'text/csv'
]

// 最大文件大小 (100MB)
const MAX_FILE_SIZE = 100 * 1024 * 1024

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const search = searchParams.get('search') || ''
    const category = searchParams.get('category') || ''
    const sortBy = searchParams.get('sortBy') || 'createdAt'
    const sortOrder = searchParams.get('sortOrder') || 'desc'

    // 这里应该从数据库获取文件列表
    // 目前返回模拟数据
    const mockFiles = [
      {
        id: '1',
        title: '海能达 CPS 2.0 写频软件',
        description: '海能达数字对讲机专用写频软件',
        filename: 'hytera-cps-2.0.zip',
        originalName: 'Hytera_CPS_2.0_Setup.zip',
        size: 15728640,
        downloadCount: 1250,
        isActive: true,
        categoryId: 'programming-software',
        tags: ['海能达', 'CPS', '写频'],
        version: '2.0.1',
        createdAt: '2024-01-15T00:00:00Z',
        updatedAt: '2024-01-15T00:00:00Z'
      },
      {
        id: '2',
        title: '摩托罗拉 CPS 16.0 写频软件',
        description: '摩托罗拉对讲机写频软件',
        filename: 'motorola-cps-16.0.exe',
        originalName: 'Motorola_CPS_16.0_Setup.exe',
        size: 89478485,
        downloadCount: 2100,
        isActive: true,
        categoryId: 'programming-software',
        tags: ['摩托罗拉', 'CPS', '写频'],
        version: '16.0.3',
        createdAt: '2024-01-10T00:00:00Z',
        updatedAt: '2024-01-10T00:00:00Z'
      }
    ]

    // 应用搜索和筛选
    let filteredFiles = mockFiles
    if (search) {
      filteredFiles = filteredFiles.filter(file => 
        file.title.toLowerCase().includes(search.toLowerCase()) ||
        file.description.toLowerCase().includes(search.toLowerCase())
      )
    }
    if (category) {
      filteredFiles = filteredFiles.filter(file => file.categoryId === category)
    }

    // 分页
    const total = filteredFiles.length
    const totalPages = Math.ceil(total / limit)
    const offset = (page - 1) * limit
    const paginatedFiles = filteredFiles.slice(offset, offset + limit)

    return NextResponse.json({
      success: true,
      data: paginatedFiles,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    })
  } catch (error) {
    console.error('获取文件列表失败:', error)
    return NextResponse.json(
      { success: false, error: '获取文件列表失败' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData()
    const file = formData.get('file') as File
    const title = formData.get('title') as string
    const description = formData.get('description') as string
    const categoryId = formData.get('categoryId') as string
    const tags = formData.get('tags') as string
    const version = formData.get('version') as string

    // 验证必填字段
    if (!file || !title || !categoryId) {
      return NextResponse.json(
        { success: false, error: '缺少必填字段' },
        { status: 400 }
      )
    }

    // 验证文件类型
    if (!ALLOWED_FILE_TYPES.includes(file.type)) {
      return NextResponse.json(
        { success: false, error: '不支持的文件类型' },
        { status: 400 }
      )
    }

    // 验证文件大小
    if (file.size > MAX_FILE_SIZE) {
      return NextResponse.json(
        { success: false, error: '文件大小超过限制' },
        { status: 400 }
      )
    }

    // 生成唯一文件名
    const fileExtension = path.extname(file.name)
    const uniqueFilename = `${nanoid()}_${Date.now()}${fileExtension}`
    
    // 确保上传目录存在
    const uploadDir = path.join(process.cwd(), 'public', 'uploads')
    if (!existsSync(uploadDir)) {
      await mkdir(uploadDir, { recursive: true })
    }

    // 保存文件
    const filePath = path.join(uploadDir, uniqueFilename)
    const bytes = await file.arrayBuffer()
    const buffer = Buffer.from(bytes)
    await writeFile(filePath, buffer)

    // 这里应该保存文件信息到数据库
    const fileData = {
      id: nanoid(),
      title,
      description,
      filename: uniqueFilename,
      originalName: file.name,
      mimeType: file.type,
      size: file.size,
      downloadCount: 0,
      isActive: true,
      categoryId,
      tags: tags ? tags.split(',').map(tag => tag.trim()) : [],
      version,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }

    console.log('文件上传成功:', fileData)

    return NextResponse.json({
      success: true,
      data: fileData,
      message: '文件上传成功'
    })
  } catch (error) {
    console.error('文件上传失败:', error)
    return NextResponse.json(
      { success: false, error: '文件上传失败' },
      { status: 500 }
    )
  }
}
