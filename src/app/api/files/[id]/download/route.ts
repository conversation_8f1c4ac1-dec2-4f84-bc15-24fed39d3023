import { NextRequest, NextResponse } from 'next/server'
import { readFile } from 'fs/promises'
import { existsSync } from 'fs'
import path from 'path'

// 模拟文件数据 - 实际项目中会从数据库获取
const getFileData = (id: string) => {
  const files: Record<string, any> = {
    '1': {
      id: '1',
      title: '海能达 CPS 2.0 写频软件',
      filename: 'hytera-cps-2.0.zip',
      originalName: 'Hytera_CPS_2.0_Setup.zip',
      mimeType: 'application/zip',
      size: 15728640,
      downloadCount: 1250,
      isActive: true
    },
    '2': {
      id: '2',
      title: '摩托罗拉 CPS 16.0 写频软件',
      filename: 'motorola-cps-16.0.exe',
      originalName: 'Motorola_CPS_16.0_Setup.exe',
      mimeType: 'application/x-msdownload',
      size: 89478485,
      downloadCount: 2100,
      isActive: true
    }
  }
  
  return files[id] || null
}

export async function GET(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const params = await context.params
    const fileId = params.id
    const fileData = getFileData(fileId)

    if (!fileData) {
      return NextResponse.json(
        { success: false, error: '文件不存在' },
        { status: 404 }
      )
    }

    if (!fileData.isActive) {
      return NextResponse.json(
        { success: false, error: '文件已被禁用' },
        { status: 403 }
      )
    }

    // 构建文件路径
    const filePath = path.join(process.cwd(), 'public', 'uploads', fileData.filename)

    // 检查文件是否存在
    if (!existsSync(filePath)) {
      return NextResponse.json(
        { success: false, error: '文件不存在于服务器' },
        { status: 404 }
      )
    }

    try {
      // 读取文件
      const fileBuffer = await readFile(filePath)

      // 获取客户端信息用于记录下载
      const userAgent = request.headers.get('user-agent') || ''
      const clientIP = request.headers.get('x-forwarded-for') || 
                      request.headers.get('x-real-ip') || 
                      'unknown'

      // 记录下载日志
      console.log('文件下载:', {
        fileId,
        filename: fileData.filename,
        clientIP,
        userAgent,
        timestamp: new Date().toISOString()
      })

      // 这里应该更新数据库中的下载次数
      // await updateDownloadCount(fileId)

      // 设置响应头
      const headers = new Headers()
      headers.set('Content-Type', fileData.mimeType)
      headers.set('Content-Length', fileData.size.toString())
      headers.set('Content-Disposition', `attachment; filename="${encodeURIComponent(fileData.originalName)}"`)
      headers.set('Cache-Control', 'no-cache, no-store, must-revalidate')
      headers.set('Pragma', 'no-cache')
      headers.set('Expires', '0')

      return new NextResponse(fileBuffer, {
        status: 200,
        headers
      })
    } catch (fileError) {
      console.error('读取文件失败:', fileError)
      return NextResponse.json(
        { success: false, error: '文件读取失败' },
        { status: 500 }
      )
    }
  } catch (error) {
    console.error('下载处理失败:', error)
    return NextResponse.json(
      { success: false, error: '下载处理失败' },
      { status: 500 }
    )
  }
}

// 获取下载信息（不实际下载文件）
export async function POST(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const params = await context.params
    const fileId = params.id
    const fileData = getFileData(fileId)

    if (!fileData) {
      return NextResponse.json(
        { success: false, error: '文件不存在' },
        { status: 404 }
      )
    }

    if (!fileData.isActive) {
      return NextResponse.json(
        { success: false, error: '文件已被禁用' },
        { status: 403 }
      )
    }

    // 检查文件是否存在于服务器
    const filePath = path.join(process.cwd(), 'public', 'uploads', fileData.filename)
    if (!existsSync(filePath)) {
      return NextResponse.json(
        { success: false, error: '文件不存在于服务器' },
        { status: 404 }
      )
    }

    // 返回下载信息
    return NextResponse.json({
      success: true,
      data: {
        id: fileData.id,
        title: fileData.title,
        filename: fileData.originalName,
        size: fileData.size,
        mimeType: fileData.mimeType,
        downloadCount: fileData.downloadCount,
        downloadUrl: `/api/files/${fileId}/download`
      }
    })
  } catch (error) {
    console.error('获取下载信息失败:', error)
    return NextResponse.json(
      { success: false, error: '获取下载信息失败' },
      { status: 500 }
    )
  }
}
