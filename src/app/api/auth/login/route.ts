import { NextRequest, NextResponse } from 'next/server'
import bcrypt from 'bcryptjs'

// 模拟管理员用户数据 - 实际项目中会从数据库获取
const adminUsers = [
  {
    id: '1',
    email: '<EMAIL>',
    password: '$2a$10$KlShv6bUylV5gbNE.PDo4eNraiieYkQzUXGR90B4wCg8mnPDczbNC', // admin123
    name: '管理员',
    role: 'ADMIN',
    isActive: true,
    createdAt: '2024-01-01T00:00:00Z'
  }
]

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { email, password } = body

    // 验证必填字段
    if (!email || !password) {
      return NextResponse.json(
        { success: false, error: '邮箱和密码不能为空' },
        { status: 400 }
      )
    }

    // 查找用户
    const user = adminUsers.find(u => u.email.toLowerCase() === email.toLowerCase())
    
    if (!user) {
      return NextResponse.json(
        { success: false, error: '用户不存在' },
        { status: 401 }
      )
    }

    // 检查用户是否激活
    if (!user.isActive) {
      return NextResponse.json(
        { success: false, error: '账户已被禁用' },
        { status: 401 }
      )
    }

    // 验证密码
    const isPasswordValid = await bcrypt.compare(password, user.password)
    
    if (!isPasswordValid) {
      return NextResponse.json(
        { success: false, error: '密码错误' },
        { status: 401 }
      )
    }

    // 创建用户会话数据（不包含密码）
    const userSession = {
      id: user.id,
      email: user.email,
      name: user.name,
      role: user.role
    }

    // 创建响应
    const response = NextResponse.json({
      success: true,
      data: {
        user: userSession,
        message: '登录成功'
      }
    })

    // 设置会话 cookie（简单实现，生产环境建议使用 JWT 或其他安全方案）
    response.cookies.set('admin-session', JSON.stringify(userSession), {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: 60 * 60 * 24 * 7 // 7天
    })

    return response

  } catch (error) {
    console.error('登录错误:', error)
    return NextResponse.json(
      { success: false, error: '服务器内部错误' },
      { status: 500 }
    )
  }
}

// 获取当前登录用户信息
export async function GET(request: NextRequest) {
  try {
    const sessionCookie = request.cookies.get('admin-session')
    
    if (!sessionCookie) {
      return NextResponse.json(
        { success: false, error: '未登录' },
        { status: 401 }
      )
    }

    const userSession = JSON.parse(sessionCookie.value)
    
    return NextResponse.json({
      success: true,
      data: { user: userSession }
    })

  } catch (error) {
    console.error('获取用户信息错误:', error)
    return NextResponse.json(
      { success: false, error: '会话无效' },
      { status: 401 }
    )
  }
}
