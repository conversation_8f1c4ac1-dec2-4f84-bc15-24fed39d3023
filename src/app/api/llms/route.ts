import { NextResponse } from 'next/server'

export async function GET() {
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'
  
  const llmsTxt = `# 对讲机软件下载站 - LLMs.txt

## 网站信息
- 网站名称: 对讲机软件下载站
- 网站地址: ${baseUrl}
- 网站描述: 专业的对讲机行业软件下载平台，提供各品牌写频软件、产品资料、技术文档等文件下载服务
- 主要服务: 对讲机软件下载、技术资料分享、产品手册提供

## 主要内容分类
- 写频软件: 各品牌对讲机写频软件，包括海能达、摩托罗拉、建伍、八重洲等
- 产品手册: 用户手册、技术手册、安装指南
- 技术资料: 技术规格、应用笔记、白皮书、案例研究、培训资料
- 驱动程序: Windows、Linux、Mac驱动程序
- 固件升级: 最新固件和历史版本

## 支持的文件格式
- 压缩文件: ZIP, RAR, 7Z
- 文档文件: PDF, DOC, DOCX
- 表格文件: XLS, XLSX
- 软件安装包: EXE, MSI
- 其他格式: TXT, CSV

## 主要品牌覆盖
- 海能达 (Hytera)
- 摩托罗拉 (Motorola)
- 建伍 (Kenwood)
- 八重洲 (Yaesu)
- 其他专业对讲机品牌

## 网站特色
- 专业性: 专注对讲机行业，内容专业可靠
- 安全性: 所有文件经过安全检测，无病毒无恶意代码
- 时效性: 定期更新最新版本软件和资料
- 便捷性: 分类清晰，搜索功能强大，下载便捷

## 主要页面
- 首页: ${baseUrl}
- 分类浏览: ${baseUrl}/categories
- 搜索页面: ${baseUrl}/search
- 关于我们: ${baseUrl}/about
- 联系我们: ${baseUrl}/contact

## 技术信息
- 框架: Next.js 15 + TypeScript
- 样式: Tailwind CSS
- 数据库: PostgreSQL + Prisma ORM
- 部署: Docker Compose

## 联系信息
- 如有技术问题或合作需求，请通过网站联系页面与我们联系
- 我们提供7×24小时技术支持服务

## 更新频率
- 软件资源: 每周更新
- 技术文档: 每月更新
- 网站内容: 持续更新

## 版权声明
- 网站内容遵循相关版权法规
- 软件资源来源于官方或授权渠道
- 仅供学习和技术交流使用

---
最后更新时间: ${new Date().toISOString().split('T')[0]}
网站地图: ${baseUrl}/sitemap.xml`

  return new NextResponse(llmsTxt, {
    headers: {
      'Content-Type': 'text/plain; charset=utf-8',
      'Cache-Control': 'public, max-age=86400, s-maxage=86400'
    }
  })
}
