import { NextResponse } from 'next/server'

export async function GET() {
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'
  
  const robots = `User-agent: *
Allow: /

# 允许搜索引擎访问的路径
Allow: /categories
Allow: /search
Allow: /files
Allow: /about
Allow: /contact
Allow: /privacy
Allow: /terms

# 禁止访问的路径
Disallow: /admin
Disallow: /api
Disallow: /_next
Disallow: /uploads

# 特殊爬虫规则
User-agent: Googlebot
Allow: /

User-agent: Bingbot
Allow: /

User-agent: Baiduspider
Allow: /

# 网站地图
Sitemap: ${baseUrl}/sitemap.xml

# 爬取延迟（毫秒）
Crawl-delay: 1`

  return new NextResponse(robots, {
    headers: {
      'Content-Type': 'text/plain',
      'Cache-Control': 'public, max-age=86400, s-maxage=86400'
    }
  })
}
