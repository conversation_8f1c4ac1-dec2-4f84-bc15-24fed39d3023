import { NextResponse } from 'next/server'

// 模拟数据 - 实际项目中会从数据库获取
const mockCategories = [
  { slug: 'programming-software', updatedAt: '2024-01-15' },
  { slug: 'manuals', updatedAt: '2024-01-12' },
  { slug: 'technical-docs', updatedAt: '2024-01-10' },
  { slug: 'drivers', updatedAt: '2024-01-08' },
  { slug: 'firmware', updatedAt: '2024-01-05' }
]

const mockFiles = [
  { id: '1', updatedAt: '2024-01-15' },
  { id: '2', updatedAt: '2024-01-10' },
  { id: '3', updatedAt: '2024-01-12' }
]

const mockPages = [
  { slug: 'about', updatedAt: '2024-01-01' },
  { slug: 'contact', updatedAt: '2024-01-01' },
  { slug: 'privacy', updatedAt: '2024-01-01' },
  { slug: 'terms', updatedAt: '2024-01-01' }
]

export async function GET() {
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'
  
  const sitemap = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"
        xmlns:news="http://www.google.com/schemas/sitemap-news/0.9"
        xmlns:xhtml="http://www.w3.org/1999/xhtml"
        xmlns:mobile="http://www.google.com/schemas/sitemap-mobile/1.0"
        xmlns:image="http://www.google.com/schemas/sitemap-image/1.1"
        xmlns:video="http://www.google.com/schemas/sitemap-video/1.1">

  <!-- 首页 -->
  <url>
    <loc>${baseUrl}</loc>
    <lastmod>${new Date().toISOString().split('T')[0]}</lastmod>
    <changefreq>daily</changefreq>
    <priority>1.0</priority>
  </url>

  <!-- 分类浏览页 -->
  <url>
    <loc>${baseUrl}/categories</loc>
    <lastmod>${new Date().toISOString().split('T')[0]}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>0.9</priority>
  </url>

  <!-- 搜索页 -->
  <url>
    <loc>${baseUrl}/search</loc>
    <lastmod>${new Date().toISOString().split('T')[0]}</lastmod>
    <changefreq>monthly</changefreq>
    <priority>0.8</priority>
  </url>

  <!-- 分类页面 -->
${mockCategories.map(category => `  <url>
    <loc>${baseUrl}/categories/${category.slug}</loc>
    <lastmod>${category.updatedAt}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>0.8</priority>
  </url>`).join('\n')}

  <!-- 文件详情页 -->
${mockFiles.map(file => `  <url>
    <loc>${baseUrl}/files/${file.id}</loc>
    <lastmod>${file.updatedAt}</lastmod>
    <changefreq>monthly</changefreq>
    <priority>0.7</priority>
  </url>`).join('\n')}

  <!-- 静态页面 -->
${mockPages.map(page => `  <url>
    <loc>${baseUrl}/${page.slug}</loc>
    <lastmod>${page.updatedAt}</lastmod>
    <changefreq>monthly</changefreq>
    <priority>0.6</priority>
  </url>`).join('\n')}

</urlset>`

  return new NextResponse(sitemap, {
    headers: {
      'Content-Type': 'application/xml',
      'Cache-Control': 'public, max-age=3600, s-maxage=3600'
    }
  })
}
