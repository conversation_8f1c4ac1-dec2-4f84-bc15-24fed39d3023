'use client'

import { useState } from 'react'
import Link from 'next/link'
import { 
  Search, 
  Filter, 
  Download, 
  FileText, 
  Calendar, 
  HardDrive,
  ChevronRight,
  X
} from 'lucide-react'
import Header from '@/components/layout/Header'
import Footer from '@/components/layout/Footer'
import But<PERSON> from '@/components/ui/Button'
import Input from '@/components/ui/Input'

// 模拟搜索结果数据
const mockSearchResults = [
  {
    id: '1',
    title: '海能达 CPS 2.0 写频软件',
    description: '海能达数字对讲机专用写频软件，支持最新型号',
    filename: 'hytera-cps-2.0.zip',
    size: 15728640,
    downloadCount: 1250,
    createdAt: '2024-01-15',
    version: '2.0.1',
    category: '写频软件',
    categorySlug: 'programming-software'
  },
  {
    id: '2',
    title: '摩托罗拉 CPS 16.0 写频软件',
    description: '摩托罗拉对讲机写频软件，兼容多个系列产品',
    filename: 'motorola-cps-16.0.exe',
    size: 89478485,
    downloadCount: 2100,
    createdAt: '2024-01-10',
    version: '16.0.3',
    category: '写频软件',
    categorySlug: 'programming-software'
  },
  {
    id: '3',
    title: '海能达 PD780 用户手册',
    description: 'PD780数字对讲机详细使用说明',
    filename: 'hytera-pd780-manual.pdf',
    size: 5242880,
    downloadCount: 650,
    createdAt: '2024-01-12',
    version: '1.0',
    category: '产品手册',
    categorySlug: 'manuals'
  },
]

const categories = [
  { name: '写频软件', slug: 'programming-software' },
  { name: '产品手册', slug: 'manuals' },
  { name: '技术资料', slug: 'technical-docs' },
  { name: '驱动程序', slug: 'drivers' },
  { name: '固件升级', slug: 'firmware' },
]

function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

function formatDate(dateString: string): string {
  return new Date(dateString).toLocaleDateString('zh-CN')
}

export default function SearchPage() {
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('')
  const [showFilters, setShowFilters] = useState(false)
  const [searchResults, setSearchResults] = useState<any[]>([])
  const [isSearching, setIsSearching] = useState(false)
  const [hasSearched, setHasSearched] = useState(false)

  const handleSearch = async () => {
    if (!searchQuery.trim()) return
    
    setIsSearching(true)
    setHasSearched(true)
    
    // 模拟搜索延迟
    setTimeout(() => {
      const filtered = mockSearchResults.filter(file => 
        file.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        file.description.toLowerCase().includes(searchQuery.toLowerCase())
      ).filter(file => 
        !selectedCategory || file.categorySlug === selectedCategory
      )
      
      setSearchResults(filtered)
      setIsSearching(false)
    }, 500)
  }

  const clearFilters = () => {
    setSelectedCategory('')
    setSearchQuery('')
    setSearchResults([])
    setHasSearched(false)
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* 面包屑导航 */}
        <nav className="flex mb-8" aria-label="Breadcrumb">
          <ol className="inline-flex items-center space-x-1 md:space-x-3">
            <li className="inline-flex items-center">
              <Link href="/" className="text-gray-700 hover:text-primary-600">
                首页
              </Link>
            </li>
            <li>
              <div className="flex items-center">
                <ChevronRight className="w-4 h-4 text-gray-400 mx-1" />
                <span className="text-gray-500">搜索</span>
              </div>
            </li>
          </ol>
        </nav>

        {/* 页面标题 */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">文件搜索</h1>
          <p className="text-lg text-gray-600">
            搜索您需要的对讲机软件、手册和技术资料
          </p>
        </div>

        {/* 搜索表单 */}
        <div className="bg-white rounded-lg shadow-md border border-gray-200 p-6 mb-8">
          <div className="flex flex-col lg:flex-row gap-4">
            <div className="flex-1">
              <Input
                placeholder="输入关键词搜索文件..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
              />
            </div>
            
            <div className="flex gap-2">
              <Button
                variant="outline"
                onClick={() => setShowFilters(!showFilters)}
              >
                <Filter className="w-4 h-4 mr-2" />
                筛选
              </Button>
              
              <Button onClick={handleSearch} loading={isSearching}>
                <Search className="w-4 h-4 mr-2" />
                搜索
              </Button>
            </div>
          </div>

          {/* 筛选器 */}
          {showFilters && (
            <div className="mt-6 pt-6 border-t border-gray-200">
              <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    文件分类
                  </label>
                  <select
                    value={selectedCategory}
                    onChange={(e) => setSelectedCategory(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                  >
                    <option value="">所有分类</option>
                    {categories.map((category) => (
                      <option key={category.slug} value={category.slug}>
                        {category.name}
                      </option>
                    ))}
                  </select>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    文件大小
                  </label>
                  <select className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500">
                    <option value="">任意大小</option>
                    <option value="small">小于 10MB</option>
                    <option value="medium">10MB - 100MB</option>
                    <option value="large">大于 100MB</option>
                  </select>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    上传时间
                  </label>
                  <select className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500">
                    <option value="">任意时间</option>
                    <option value="week">最近一周</option>
                    <option value="month">最近一月</option>
                    <option value="year">最近一年</option>
                  </select>
                </div>
              </div>
              
              {(selectedCategory || searchQuery) && (
                <div className="mt-4">
                  <Button variant="outline" size="sm" onClick={clearFilters}>
                    <X className="w-4 h-4 mr-2" />
                    清除筛选
                  </Button>
                </div>
              )}
            </div>
          )}
        </div>

        {/* 搜索结果 */}
        {hasSearched && (
          <div>
            {isSearching ? (
              <div className="text-center py-12">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4"></div>
                <p className="text-gray-600">正在搜索...</p>
              </div>
            ) : (
              <>
                {/* 结果统计 */}
                <div className="mb-6">
                  <p className="text-gray-600">
                    找到 <span className="font-semibold text-gray-900">{searchResults.length}</span> 个相关文件
                    {searchQuery && (
                      <>
                        ，关键词: <span className="font-semibold text-primary-600">"{searchQuery}"</span>
                      </>
                    )}
                  </p>
                </div>

                {/* 搜索结果列表 */}
                {searchResults.length > 0 ? (
                  <div className="space-y-4">
                    {searchResults.map((file) => (
                      <div key={file.id} className="bg-white rounded-lg shadow-md border border-gray-200 p-6">
                        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
                          <div className="flex-1">
                            <div className="flex items-start justify-between mb-2">
                              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                                <Link 
                                  href={`/files/${file.id}`}
                                  className="hover:text-primary-600 transition-colors"
                                >
                                  {file.title}
                                </Link>
                              </h3>
                              <span className="bg-primary-100 text-primary-800 text-xs font-medium px-2.5 py-0.5 rounded">
                                {file.category}
                              </span>
                            </div>
                            
                            <p className="text-gray-600 mb-4">{file.description}</p>
                            
                            <div className="flex flex-wrap items-center gap-4 text-sm text-gray-500">
                              <div className="flex items-center">
                                <HardDrive className="w-4 h-4 mr-1" />
                                <span>{formatFileSize(file.size)}</span>
                              </div>
                              <div className="flex items-center">
                                <Download className="w-4 h-4 mr-1" />
                                <span>{file.downloadCount} 次下载</span>
                              </div>
                              <div className="flex items-center">
                                <Calendar className="w-4 h-4 mr-1" />
                                <span>{formatDate(file.createdAt)}</span>
                              </div>
                              {file.version && (
                                <span className="bg-gray-100 text-gray-800 text-xs px-2 py-1 rounded">
                                  v{file.version}
                                </span>
                              )}
                            </div>
                          </div>
                          
                          <div className="mt-4 lg:mt-0 lg:ml-6">
                            <Link href={`/files/${file.id}`}>
                              <Button>
                                <Download className="w-4 h-4 mr-2" />
                                下载
                              </Button>
                            </Link>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-12">
                    <FileText className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">未找到相关文件</h3>
                    <p className="text-gray-600 mb-4">
                      请尝试使用不同的关键词或调整筛选条件
                    </p>
                    <Button variant="outline" onClick={clearFilters}>
                      清除筛选条件
                    </Button>
                  </div>
                )}
              </>
            )}
          </div>
        )}

        {/* 热门搜索建议 */}
        {!hasSearched && (
          <div className="bg-white rounded-lg shadow-md border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">热门搜索</h3>
            <div className="flex flex-wrap gap-2">
              {['海能达', '摩托罗拉', '建伍', '写频软件', '用户手册', '驱动程序'].map((keyword) => (
                <button
                  key={keyword}
                  onClick={() => {
                    setSearchQuery(keyword)
                    handleSearch()
                  }}
                  className="px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-sm hover:bg-gray-200 transition-colors"
                >
                  {keyword}
                </button>
              ))}
            </div>
          </div>
        )}
      </main>

      <Footer />
    </div>
  )
}
