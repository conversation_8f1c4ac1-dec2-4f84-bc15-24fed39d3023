import Link from 'next/link'
import { Metadata } from 'next'
import { Folder, FileText, Download, ChevronRight } from 'lucide-react'
import Header from '@/components/layout/Header'
import Footer from '@/components/layout/Footer'

export const metadata: Metadata = {
  title: '分类浏览',
  description: '浏览所有文件分类，快速找到您需要的对讲机软件和资料',
  keywords: ['分类', '浏览', '对讲机软件', '写频软件', '产品手册'],
}

// 模拟数据 - 实际项目中会从数据库获取
const categories = [
  {
    id: '1',
    name: '写频软件',
    slug: 'programming-software',
    description: '各品牌对讲机写频软件',
    fileCount: 120,
    children: [
      { id: '11', name: '海能达', slug: 'hytera', fileCount: 25 },
      { id: '12', name: '摩托罗拉', slug: 'motorola', fileCount: 30 },
      { id: '13', name: '建伍', slug: 'kenwood', fileCount: 28 },
      { id: '14', name: '八重洲', slug: 'yaesu', fileCount: 20 },
      { id: '15', name: '其他品牌', slug: 'others', fileCount: 17 },
    ]
  },
  {
    id: '2',
    name: '产品手册',
    slug: 'manuals',
    description: '产品使用手册和说明书',
    fileCount: 80,
    children: [
      { id: '21', name: '用户手册', slug: 'user-manuals', fileCount: 35 },
      { id: '22', name: '技术手册', slug: 'technical-manuals', fileCount: 25 },
      { id: '23', name: '安装指南', slug: 'installation-guides', fileCount: 20 },
    ]
  },
  {
    id: '3',
    name: '技术资料',
    slug: 'technical-docs',
    description: '技术文档和参考资料',
    fileCount: 150,
    children: [
      { id: '31', name: '技术规格', slug: 'specifications', fileCount: 40 },
      { id: '32', name: '应用笔记', slug: 'application-notes', fileCount: 35 },
      { id: '33', name: '白皮书', slug: 'whitepapers', fileCount: 30 },
      { id: '34', name: '案例研究', slug: 'case-studies', fileCount: 25 },
      { id: '35', name: '培训资料', slug: 'training-materials', fileCount: 20 },
    ]
  },
  {
    id: '4',
    name: '驱动程序',
    slug: 'drivers',
    description: '设备驱动程序',
    fileCount: 60,
    children: [
      { id: '41', name: 'Windows驱动', slug: 'windows-drivers', fileCount: 35 },
      { id: '42', name: 'Linux驱动', slug: 'linux-drivers', fileCount: 15 },
      { id: '43', name: 'Mac驱动', slug: 'mac-drivers', fileCount: 10 },
    ]
  },
  {
    id: '5',
    name: '固件升级',
    slug: 'firmware',
    description: '设备固件升级文件',
    fileCount: 45,
    children: [
      { id: '51', name: '最新固件', slug: 'latest-firmware', fileCount: 25 },
      { id: '52', name: '历史版本', slug: 'legacy-firmware', fileCount: 20 },
    ]
  },
]

export default function CategoriesPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* 页面标题 */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">文件分类浏览</h1>
          <p className="text-lg text-gray-600">
            按分类浏览所有文件，快速找到您需要的对讲机软件和资料
          </p>
        </div>

        {/* 面包屑导航 */}
        <nav className="flex mb-8" aria-label="Breadcrumb">
          <ol className="inline-flex items-center space-x-1 md:space-x-3">
            <li className="inline-flex items-center">
              <Link href="/" className="text-gray-700 hover:text-primary-600">
                首页
              </Link>
            </li>
            <li>
              <div className="flex items-center">
                <ChevronRight className="w-4 h-4 text-gray-400 mx-1" />
                <span className="text-gray-500">分类浏览</span>
              </div>
            </li>
          </ol>
        </nav>

        {/* 分类网格 */}
        <div className="grid gap-8">
          {categories.map((category) => (
            <div key={category.id} className="bg-white rounded-lg shadow-md border border-gray-200 p-6">
              {/* 分类标题 */}
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center">
                  <Folder className="w-8 h-8 text-primary-600 mr-3" />
                  <div>
                    <h2 className="text-xl font-semibold text-gray-900">
                      <Link 
                        href={`/categories/${category.slug}`}
                        className="hover:text-primary-600 transition-colors"
                      >
                        {category.name}
                      </Link>
                    </h2>
                    <p className="text-gray-600">{category.description}</p>
                  </div>
                </div>
                <div className="text-right">
                  <div className="flex items-center text-gray-500">
                    <FileText className="w-4 h-4 mr-1" />
                    <span className="text-sm">{category.fileCount} 个文件</span>
                  </div>
                </div>
              </div>

              {/* 子分类 */}
              {category.children && category.children.length > 0 && (
                <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {category.children.map((child) => (
                    <Link
                      key={child.id}
                      href={`/categories/${category.slug}/${child.slug}`}
                      className="block p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
                    >
                      <div className="flex items-center justify-between">
                        <span className="font-medium text-gray-900">{child.name}</span>
                        <span className="text-sm text-gray-500">{child.fileCount}</span>
                      </div>
                    </Link>
                  ))}
                </div>
              )}

              {/* 查看全部按钮 */}
              <div className="mt-4 pt-4 border-t border-gray-200">
                <Link
                  href={`/categories/${category.slug}`}
                  className="inline-flex items-center text-primary-600 hover:text-primary-700 font-medium"
                >
                  <Download className="w-4 h-4 mr-2" />
                  查看所有 {category.name}
                  <ChevronRight className="w-4 h-4 ml-1" />
                </Link>
              </div>
            </div>
          ))}
        </div>

        {/* 统计信息 */}
        <div className="mt-12 bg-primary-50 rounded-lg p-6">
          <div className="grid md:grid-cols-4 gap-6 text-center">
            <div>
              <div className="text-2xl font-bold text-primary-600">
                {categories.reduce((sum, cat) => sum + cat.fileCount, 0)}
              </div>
              <div className="text-sm text-gray-600">总文件数</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-primary-600">{categories.length}</div>
              <div className="text-sm text-gray-600">主要分类</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-primary-600">
                {categories.reduce((sum, cat) => sum + (cat.children?.length || 0), 0)}
              </div>
              <div className="text-sm text-gray-600">子分类</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-primary-600">24/7</div>
              <div className="text-sm text-gray-600">全天候服务</div>
            </div>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  )
}
