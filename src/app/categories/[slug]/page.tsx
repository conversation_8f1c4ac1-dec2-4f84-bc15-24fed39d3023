import Link from 'next/link'
import { Metadata } from 'next'
import { notFound } from 'next/navigation'
import { 
  ChevronRight, 
  Download, 
  FileText, 
  Calendar, 
  HardDrive,
  Filter,
  Grid,
  List
} from 'lucide-react'
import Header from '@/components/layout/Header'
import Footer from '@/components/layout/Footer'
import Button from '@/components/ui/Button'

interface CategoryPageProps {
  params: Promise<{
    slug: string
  }>
}

// 模拟数据 - 实际项目中会从数据库获取
const getCategoryData = (slug: string) => {
  const categories: Record<string, any> = {
    'programming-software': {
      id: '1',
      name: '写频软件',
      slug: 'programming-software',
      description: '各品牌对讲机写频软件，支持频率设置、信道编程等功能',
      files: [
        {
          id: '1',
          title: '海能达 CPS 2.0 写频软件',
          description: '海能达数字对讲机专用写频软件，支持最新型号',
          filename: 'hytera-cps-2.0.zip',
          size: 15728640, // 15MB
          downloadCount: 1250,
          createdAt: '2024-01-15',
          version: '2.0.1',
          category: '海能达'
        },
        {
          id: '2',
          title: '摩托罗拉 CPS 16.0 写频软件',
          description: '摩托罗拉对讲机写频软件，兼容多个系列产品',
          filename: 'motorola-cps-16.0.exe',
          size: 89478485, // 85MB
          downloadCount: 2100,
          createdAt: '2024-01-10',
          version: '16.0.3',
          category: '摩托罗拉'
        },
        {
          id: '3',
          title: '建伍 KPG-D1N 写频软件',
          description: '建伍数字对讲机写频软件，支持DMR协议',
          filename: 'kenwood-kpg-d1n.zip',
          size: 25165824, // 24MB
          downloadCount: 890,
          createdAt: '2024-01-08',
          version: '3.2.1',
          category: '建伍'
        },
      ]
    },
    'manuals': {
      id: '2',
      name: '产品手册',
      slug: 'manuals',
      description: '产品使用手册、技术手册和安装指南',
      files: [
        {
          id: '4',
          title: '海能达 PD780 用户手册',
          description: 'PD780数字对讲机详细使用说明',
          filename: 'hytera-pd780-manual.pdf',
          size: 5242880, // 5MB
          downloadCount: 650,
          createdAt: '2024-01-12',
          version: '1.0',
          category: '用户手册'
        },
      ]
    }
  }
  
  return categories[slug] || null
}

export async function generateMetadata({ params }: CategoryPageProps): Promise<Metadata> {
  const resolvedParams = await params
  const category = getCategoryData(resolvedParams.slug)
  
  if (!category) {
    return {
      title: '分类未找到',
    }
  }

  return {
    title: category.name,
    description: category.description,
    keywords: [category.name, '下载', '对讲机', '软件'],
  }
}

function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

function formatDate(dateString: string): string {
  return new Date(dateString).toLocaleDateString('zh-CN')
}

export default async function CategoryPage({ params }: CategoryPageProps) {
  const resolvedParams = await params
  const category = getCategoryData(resolvedParams.slug)
  
  if (!category) {
    notFound()
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* 面包屑导航 */}
        <nav className="flex mb-8" aria-label="Breadcrumb">
          <ol className="inline-flex items-center space-x-1 md:space-x-3">
            <li className="inline-flex items-center">
              <Link href="/" className="text-gray-700 hover:text-primary-600">
                首页
              </Link>
            </li>
            <li>
              <div className="flex items-center">
                <ChevronRight className="w-4 h-4 text-gray-400 mx-1" />
                <Link href="/categories" className="text-gray-700 hover:text-primary-600">
                  分类浏览
                </Link>
              </div>
            </li>
            <li>
              <div className="flex items-center">
                <ChevronRight className="w-4 h-4 text-gray-400 mx-1" />
                <span className="text-gray-500">{category.name}</span>
              </div>
            </li>
          </ol>
        </nav>

        {/* 分类标题和描述 */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">{category.name}</h1>
          <p className="text-lg text-gray-600 mb-6">{category.description}</p>
          
          {/* 统计信息 */}
          <div className="flex items-center space-x-6 text-sm text-gray-500">
            <div className="flex items-center">
              <FileText className="w-4 h-4 mr-1" />
              <span>{category.files.length} 个文件</span>
            </div>
            <div className="flex items-center">
              <Download className="w-4 h-4 mr-1" />
              <span>{category.files.reduce((sum: number, file: any) => sum + file.downloadCount, 0)} 次下载</span>
            </div>
          </div>
        </div>

        {/* 工具栏 */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 space-y-4 sm:space-y-0">
          <div className="flex items-center space-x-4">
            <Button variant="outline" size="sm">
              <Filter className="w-4 h-4 mr-2" />
              筛选
            </Button>
            <select className="px-3 py-2 border border-gray-300 rounded-md text-sm">
              <option>按下载量排序</option>
              <option>按时间排序</option>
              <option>按文件大小排序</option>
            </select>
          </div>
          
          <div className="flex items-center space-x-2">
            <Button variant="ghost" size="sm">
              <Grid className="w-4 h-4" />
            </Button>
            <Button variant="ghost" size="sm">
              <List className="w-4 h-4" />
            </Button>
          </div>
        </div>

        {/* 文件列表 */}
        <div className="space-y-4">
          {category.files.map((file: any) => (
            <div key={file.id} className="bg-white rounded-lg shadow-md border border-gray-200 p-6">
              <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
                <div className="flex-1">
                  <div className="flex items-start justify-between mb-2">
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">
                      <Link 
                        href={`/files/${file.id}`}
                        className="hover:text-primary-600 transition-colors"
                      >
                        {file.title}
                      </Link>
                    </h3>
                    <span className="bg-primary-100 text-primary-800 text-xs font-medium px-2.5 py-0.5 rounded">
                      {file.category}
                    </span>
                  </div>
                  
                  <p className="text-gray-600 mb-4">{file.description}</p>
                  
                  <div className="flex flex-wrap items-center gap-4 text-sm text-gray-500">
                    <div className="flex items-center">
                      <HardDrive className="w-4 h-4 mr-1" />
                      <span>{formatFileSize(file.size)}</span>
                    </div>
                    <div className="flex items-center">
                      <Download className="w-4 h-4 mr-1" />
                      <span>{file.downloadCount} 次下载</span>
                    </div>
                    <div className="flex items-center">
                      <Calendar className="w-4 h-4 mr-1" />
                      <span>{formatDate(file.createdAt)}</span>
                    </div>
                    {file.version && (
                      <span className="bg-gray-100 text-gray-800 text-xs px-2 py-1 rounded">
                        v{file.version}
                      </span>
                    )}
                  </div>
                </div>
                
                <div className="mt-4 lg:mt-0 lg:ml-6">
                  <Link href={`/files/${file.id}`}>
                    <Button>
                      <Download className="w-4 h-4 mr-2" />
                      下载
                    </Button>
                  </Link>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* 分页 */}
        <div className="mt-8 flex justify-center">
          <nav className="flex items-center space-x-2">
            <Button variant="outline" size="sm" disabled>
              上一页
            </Button>
            <Button variant="outline" size="sm" className="bg-primary-600 text-white">
              1
            </Button>
            <Button variant="outline" size="sm">
              2
            </Button>
            <Button variant="outline" size="sm">
              3
            </Button>
            <Button variant="outline" size="sm">
              下一页
            </Button>
          </nav>
        </div>
      </main>

      <Footer />
    </div>
  )
}
