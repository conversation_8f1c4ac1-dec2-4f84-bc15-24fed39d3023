import Link from 'next/link'
import { Metadata } from 'next'
import { notFound } from 'next/navigation'
import {
  ChevronRight,
  Download,
  FileText,
  Calendar,
  HardDrive,
  User,
  Eye,
  Shield,
  Star,
  AlertTriangle
} from 'lucide-react'
import Header from '@/components/layout/Header'
import Footer from '@/components/layout/Footer'
import Button from '@/components/ui/Button'
import StructuredData from '@/components/seo/StructuredData'

interface FilePageProps {
  params: Promise<{
    id: string
  }>
}

// 模拟数据 - 实际项目中会从数据库获取
const getFileData = (id: string) => {
  const files: Record<string, any> = {
    '1': {
      id: '1',
      title: '海能达 CPS 2.0 写频软件',
      description: '海能达数字对讲机专用写频软件，支持最新型号的频率设置、信道编程、功能配置等操作。该软件兼容海能达PD系列、MD系列等多个产品线。',
      filename: 'hytera-cps-2.0.zip',
      originalName: 'Hytera_CPS_2.0_Setup.zip',
      size: 15728640, // 15MB
      downloadCount: 1250,
      createdAt: '2024-01-15',
      updatedAt: '2024-01-15',
      version: '2.0.1',
      category: {
        name: '写频软件',
        slug: 'programming-software'
      },
      tags: ['海能达', 'CPS', '写频', '数字对讲机'],
      mimeType: 'application/zip',
      isActive: true,
      uploadedBy: {
        name: '管理员',
        role: 'ADMIN'
      },
      requirements: [
        'Windows 7/8/10/11 (32位或64位)',
        '至少 100MB 可用磁盘空间',
        'USB 接口用于连接对讲机',
        '管理员权限'
      ],
      features: [
        '支持频率设置和信道编程',
        '功能键自定义配置',
        '联系人和群组管理',
        '扫描列表设置',
        '加密功能配置',
        '固件升级支持'
      ],
      compatibleModels: [
        'PD780/PD780G',
        'PD680/PD680G', 
        'PD560/PD560G',
        'MD780/MD780G',
        'MD680/MD680G'
      ]
    },
    '2': {
      id: '2',
      title: '摩托罗拉 CPS 16.0 写频软件',
      description: '摩托罗拉对讲机写频软件，兼容多个系列产品',
      filename: 'motorola-cps-16.0.exe',
      size: 89478485,
      downloadCount: 2100,
      createdAt: '2024-01-10',
      version: '16.0.3',
      category: {
        name: '写频软件',
        slug: 'programming-software'
      },
      tags: ['摩托罗拉', 'CPS', '写频'],
      mimeType: 'application/x-msdownload'
    }
  }
  
  return files[id] || null
}

export async function generateMetadata({ params }: FilePageProps): Promise<Metadata> {
  const resolvedParams = await params
  const file = getFileData(resolvedParams.id)
  
  if (!file) {
    return {
      title: '文件未找到',
    }
  }

  return {
    title: file.title,
    description: file.description,
    keywords: file.tags,
    openGraph: {
      title: file.title,
      description: file.description,
      type: 'article',
    },
  }
}

function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

function formatDate(dateString: string): string {
  return new Date(dateString).toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}

function getFileIcon(mimeType: string): string {
  if (mimeType.includes('zip') || mimeType.includes('rar') || mimeType.includes('7z')) return '📦'
  if (mimeType.includes('pdf')) return '📄'
  if (mimeType.includes('word') || mimeType.includes('document')) return '📝'
  if (mimeType.includes('excel') || mimeType.includes('spreadsheet')) return '📊'
  if (mimeType.includes('exe') || mimeType.includes('msi')) return '⚙️'
  return '📁'
}

export default async function FilePage({ params }: FilePageProps) {
  const resolvedParams = await params
  const file = getFileData(resolvedParams.id)
  
  if (!file) {
    notFound()
  }

  const handleDownload = () => {
    // 这里会实现实际的下载逻辑
    console.log('下载文件:', file.filename)
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <StructuredData
        type="software"
        data={{
          id: file.id,
          title: file.title,
          description: file.description,
          size: file.size,
          version: file.version,
          createdAt: file.createdAt,
          updatedAt: file.updatedAt || file.createdAt
        }}
      />
      <StructuredData
        type="breadcrumb"
        data={{
          items: [
            { name: '首页', url: '/' },
            { name: '分类浏览', url: '/categories' },
            { name: file.category.name, url: `/categories/${file.category.slug}` },
            { name: file.title, url: `/files/${file.id}` }
          ]
        }}
      />
      <Header />
      
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* 面包屑导航 */}
        <nav className="flex mb-8" aria-label="Breadcrumb">
          <ol className="inline-flex items-center space-x-1 md:space-x-3">
            <li className="inline-flex items-center">
              <Link href="/" className="text-gray-700 hover:text-primary-600">
                首页
              </Link>
            </li>
            <li>
              <div className="flex items-center">
                <ChevronRight className="w-4 h-4 text-gray-400 mx-1" />
                <Link href="/categories" className="text-gray-700 hover:text-primary-600">
                  分类浏览
                </Link>
              </div>
            </li>
            <li>
              <div className="flex items-center">
                <ChevronRight className="w-4 h-4 text-gray-400 mx-1" />
                <Link 
                  href={`/categories/${file.category.slug}`}
                  className="text-gray-700 hover:text-primary-600"
                >
                  {file.category.name}
                </Link>
              </div>
            </li>
            <li>
              <div className="flex items-center">
                <ChevronRight className="w-4 h-4 text-gray-400 mx-1" />
                <span className="text-gray-500">{file.title}</span>
              </div>
            </li>
          </ol>
        </nav>

        <div className="grid lg:grid-cols-3 gap-8">
          {/* 主要内容 */}
          <div className="lg:col-span-2">
            {/* 文件标题和基本信息 */}
            <div className="bg-white rounded-lg shadow-md border border-gray-200 p-6 mb-6">
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center">
                  <span className="text-4xl mr-4">{getFileIcon(file.mimeType)}</span>
                  <div>
                    <h1 className="text-2xl font-bold text-gray-900 mb-2">{file.title}</h1>
                    <div className="flex items-center space-x-4 text-sm text-gray-500">
                      <span className="bg-primary-100 text-primary-800 px-2 py-1 rounded text-xs font-medium">
                        {file.category.name}
                      </span>
                      {file.version && (
                        <span className="bg-gray-100 text-gray-800 px-2 py-1 rounded text-xs">
                          v{file.version}
                        </span>
                      )}
                    </div>
                  </div>
                </div>
              </div>

              <p className="text-gray-600 mb-6">{file.description}</p>

              {/* 标签 */}
              {file.tags && file.tags.length > 0 && (
                <div className="mb-6">
                  <h3 className="text-sm font-medium text-gray-700 mb-2">标签</h3>
                  <div className="flex flex-wrap gap-2">
                    {file.tags.map((tag: string) => (
                      <span 
                        key={tag}
                        className="bg-gray-100 text-gray-700 px-2 py-1 rounded text-sm"
                      >
                        {tag}
                      </span>
                    ))}
                  </div>
                </div>
              )}

              {/* 系统要求 */}
              {file.requirements && (
                <div className="mb-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-3">系统要求</h3>
                  <ul className="space-y-2">
                    {file.requirements.map((req: string, index: number) => (
                      <li key={index} className="flex items-center text-gray-600">
                        <div className="w-2 h-2 bg-primary-600 rounded-full mr-3"></div>
                        {req}
                      </li>
                    ))}
                  </ul>
                </div>
              )}

              {/* 主要功能 */}
              {file.features && (
                <div className="mb-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-3">主要功能</h3>
                  <ul className="space-y-2">
                    {file.features.map((feature: string, index: number) => (
                      <li key={index} className="flex items-center text-gray-600">
                        <Star className="w-4 h-4 text-yellow-500 mr-3" />
                        {feature}
                      </li>
                    ))}
                  </ul>
                </div>
              )}

              {/* 兼容型号 */}
              {file.compatibleModels && (
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-3">兼容型号</h3>
                  <div className="grid md:grid-cols-2 gap-2">
                    {file.compatibleModels.map((model: string, index: number) => (
                      <div key={index} className="bg-gray-50 px-3 py-2 rounded text-sm text-gray-700">
                        {model}
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>

            {/* 安全提示 */}
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <div className="flex items-start">
                <AlertTriangle className="w-5 h-5 text-yellow-600 mr-3 mt-0.5" />
                <div>
                  <h4 className="text-sm font-medium text-yellow-800 mb-1">安全提示</h4>
                  <p className="text-sm text-yellow-700">
                    请确保从官方渠道下载软件，使用前请关闭杀毒软件的实时保护功能，避免误报。
                    建议在虚拟机或测试环境中先行测试。
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* 侧边栏 */}
          <div className="lg:col-span-1">
            {/* 下载卡片 */}
            <div className="bg-white rounded-lg shadow-md border border-gray-200 p-6 mb-6">
              <div className="text-center mb-6">
                <Button size="lg" className="w-full" onClick={handleDownload}>
                  <Download className="w-5 h-5 mr-2" />
                  立即下载
                </Button>
              </div>

              <div className="space-y-4 text-sm">
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">文件大小</span>
                  <span className="font-medium">{formatFileSize(file.size)}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">下载次数</span>
                  <span className="font-medium">{file.downloadCount.toLocaleString()}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">上传时间</span>
                  <span className="font-medium">{formatDate(file.createdAt)}</span>
                </div>
                {file.uploadedBy && (
                  <div className="flex items-center justify-between">
                    <span className="text-gray-600">上传者</span>
                    <span className="font-medium">{file.uploadedBy.name}</span>
                  </div>
                )}
              </div>
            </div>

            {/* 文件信息 */}
            <div className="bg-white rounded-lg shadow-md border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">文件信息</h3>
              <div className="space-y-3 text-sm">
                <div className="flex items-center">
                  <FileText className="w-4 h-4 text-gray-400 mr-3" />
                  <div>
                    <div className="font-medium text-gray-900">原始文件名</div>
                    <div className="text-gray-600">{file.originalName || file.filename}</div>
                  </div>
                </div>
                <div className="flex items-center">
                  <HardDrive className="w-4 h-4 text-gray-400 mr-3" />
                  <div>
                    <div className="font-medium text-gray-900">文件类型</div>
                    <div className="text-gray-600">{file.mimeType}</div>
                  </div>
                </div>
                <div className="flex items-center">
                  <Shield className="w-4 h-4 text-gray-400 mr-3" />
                  <div>
                    <div className="font-medium text-gray-900">安全状态</div>
                    <div className="text-green-600">已检测 ✓</div>
                  </div>
                </div>
                <div className="flex items-center">
                  <Eye className="w-4 h-4 text-gray-400 mr-3" />
                  <div>
                    <div className="font-medium text-gray-900">文件状态</div>
                    <div className="text-green-600">
                      {file.isActive ? '可用' : '不可用'}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  )
}
