import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'
import AdSenseScript from '@/components/ads/AdSenseScript'
import StructuredData from '@/components/seo/StructuredData'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: {
    default: '对讲机软件下载站 - 专业写频软件与产品资料',
    template: '%s | 对讲机软件下载站'
  },
  description: '专业的对讲机行业软件下载平台，提供各品牌写频软件、产品资料、技术文档等文件下载服务。支持zip、rar、7z、pdf、docs、excel等多种格式。',
  keywords: ['对讲机', '写频软件', '产品资料', '技术文档', '下载', '软件'],
  authors: [{ name: '对讲机软件下载站' }],
  creator: '对讲机软件下载站',
  publisher: '对讲机软件下载站',
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL(process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'),
  alternates: {
    canonical: '/',
  },
  openGraph: {
    type: 'website',
    locale: 'zh_CN',
    url: '/',
    title: '对讲机软件下载站 - 专业写频软件与产品资料',
    description: '专业的对讲机行业软件下载平台，提供各品牌写频软件、产品资料、技术文档等文件下载服务。',
    siteName: '对讲机软件下载站',
  },
  twitter: {
    card: 'summary_large_image',
    title: '对讲机软件下载站 - 专业写频软件与产品资料',
    description: '专业的对讲机行业软件下载平台，提供各品牌写频软件、产品资料、技术文档等文件下载服务。',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="zh-CN">
      <head>
        <AdSenseScript />
      </head>
      <body className={inter.className}>
        <StructuredData type="website" data={{}} />
        <StructuredData type="organization" data={{}} />
        {children}
      </body>
    </html>
  )
}
