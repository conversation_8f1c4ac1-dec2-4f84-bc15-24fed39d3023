import Link from 'next/link'
import { Metadata } from 'next'
import { ChevronRight, Target, Users, Shield, Zap, Award, Globe } from 'lucide-react'
import Header from '@/components/layout/Header'
import Footer from '@/components/layout/Footer'

export const metadata: Metadata = {
  title: '关于我们',
  description: '了解对讲机软件下载站的使命、愿景和服务承诺，我们致力于为对讲机行业提供专业的软件下载服务。',
  keywords: ['关于我们', '对讲机软件', '下载站', '服务', '使命'],
}

export default function AboutPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* 面包屑导航 */}
        <nav className="flex mb-8" aria-label="Breadcrumb">
          <ol className="inline-flex items-center space-x-1 md:space-x-3">
            <li className="inline-flex items-center">
              <Link href="/" className="text-gray-700 hover:text-primary-600">
                首页
              </Link>
            </li>
            <li>
              <div className="flex items-center">
                <ChevronRight className="w-4 h-4 text-gray-400 mx-1" />
                <span className="text-gray-500">关于我们</span>
              </div>
            </li>
          </ol>
        </nav>

        {/* 页面标题 */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">关于我们</h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            专业的对讲机软件下载平台，致力于为对讲机行业提供最优质的软件资源和技术支持
          </p>
        </div>

        {/* 使命愿景 */}
        <div className="grid md:grid-cols-2 gap-8 mb-16">
          <div className="bg-white rounded-lg shadow-md border border-gray-200 p-8">
            <div className="flex items-center mb-4">
              <Target className="w-8 h-8 text-primary-600 mr-3" />
              <h2 className="text-2xl font-bold text-gray-900">我们的使命</h2>
            </div>
            <p className="text-gray-600 leading-relaxed">
              为对讲机行业从业者、爱好者和技术人员提供一个专业、安全、便捷的软件下载平台。
              我们致力于收集、整理和分享各品牌对讲机的写频软件、技术文档和相关资料，
              帮助用户快速找到所需的工具和信息，提升工作效率。
            </p>
          </div>

          <div className="bg-white rounded-lg shadow-md border border-gray-200 p-8">
            <div className="flex items-center mb-4">
              <Globe className="w-8 h-8 text-primary-600 mr-3" />
              <h2 className="text-2xl font-bold text-gray-900">我们的愿景</h2>
            </div>
            <p className="text-gray-600 leading-relaxed">
              成为对讲机行业最受信赖的软件资源平台，构建一个开放、共享的技术社区。
              通过持续的技术创新和服务优化，为全球对讲机用户提供最全面的软件支持，
              推动整个行业的技术进步和发展。
            </p>
          </div>
        </div>

        {/* 核心优势 */}
        <div className="mb-16">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">我们的优势</h2>
            <p className="text-lg text-gray-600">专业、安全、便捷的服务体验</p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="bg-primary-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                <Shield className="w-8 h-8 text-primary-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3">安全可靠</h3>
              <p className="text-gray-600">
                所有文件经过严格的安全检测，确保无病毒、无恶意代码。
                采用多重验证机制，保障用户下载安全。
              </p>
            </div>

            <div className="text-center">
              <div className="bg-primary-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                <Users className="w-8 h-8 text-primary-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3">专业团队</h3>
              <p className="text-gray-600">
                由对讲机行业资深技术人员组成的专业团队，
                深度了解行业需求，提供精准的资源匹配。
              </p>
            </div>

            <div className="text-center">
              <div className="bg-primary-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                <Zap className="w-8 h-8 text-primary-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3">快速更新</h3>
              <p className="text-gray-600">
                紧跟行业发展趋势，及时更新最新版本软件和技术资料，
                确保用户获得最新的工具支持。
              </p>
            </div>
          </div>
        </div>

        {/* 服务统计 */}
        <div className="bg-primary-600 rounded-lg p-8 mb-16 text-white">
          <div className="text-center mb-8">
            <h2 className="text-3xl font-bold mb-4">服务数据</h2>
            <p className="text-primary-100">用数据说话，见证我们的成长</p>
          </div>

          <div className="grid md:grid-cols-4 gap-8 text-center">
            <div>
              <div className="text-4xl font-bold mb-2">500+</div>
              <div className="text-primary-100">软件资源</div>
            </div>
            <div>
              <div className="text-4xl font-bold mb-2">50+</div>
              <div className="text-primary-100">支持品牌</div>
            </div>
            <div>
              <div className="text-4xl font-bold mb-2">10K+</div>
              <div className="text-primary-100">用户下载</div>
            </div>
            <div>
              <div className="text-4xl font-bold mb-2">99.9%</div>
              <div className="text-primary-100">服务可用性</div>
            </div>
          </div>
        </div>

        {/* 服务承诺 */}
        <div className="bg-white rounded-lg shadow-md border border-gray-200 p-8 mb-16">
          <div className="flex items-center mb-6">
            <Award className="w-8 h-8 text-primary-600 mr-3" />
            <h2 className="text-2xl font-bold text-gray-900">服务承诺</h2>
          </div>

          <div className="grid md:grid-cols-2 gap-8">
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-3">质量保证</h3>
              <ul className="space-y-2 text-gray-600">
                <li className="flex items-center">
                  <div className="w-2 h-2 bg-primary-600 rounded-full mr-3"></div>
                  所有软件均来自官方或可信渠道
                </li>
                <li className="flex items-center">
                  <div className="w-2 h-2 bg-primary-600 rounded-full mr-3"></div>
                  定期更新软件版本和安全补丁
                </li>
                <li className="flex items-center">
                  <div className="w-2 h-2 bg-primary-600 rounded-full mr-3"></div>
                  提供详细的软件说明和使用指南
                </li>
              </ul>
            </div>

            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-3">技术支持</h3>
              <ul className="space-y-2 text-gray-600">
                <li className="flex items-center">
                  <div className="w-2 h-2 bg-primary-600 rounded-full mr-3"></div>
                  7×24小时在线技术支持
                </li>
                <li className="flex items-center">
                  <div className="w-2 h-2 bg-primary-600 rounded-full mr-3"></div>
                  专业技术人员答疑解惑
                </li>
                <li className="flex items-center">
                  <div className="w-2 h-2 bg-primary-600 rounded-full mr-3"></div>
                  定期发布技术文章和教程
                </li>
              </ul>
            </div>
          </div>
        </div>

        {/* 联系我们 */}
        <div className="text-center bg-gray-100 rounded-lg p-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">联系我们</h2>
          <p className="text-gray-600 mb-6">
            如果您有任何问题、建议或合作意向，欢迎随时联系我们
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link 
              href="/contact"
              className="bg-primary-600 text-white px-6 py-3 rounded-lg hover:bg-primary-700 transition-colors"
            >
              联系我们
            </Link>
            <Link 
              href="/categories"
              className="bg-white text-primary-600 border border-primary-600 px-6 py-3 rounded-lg hover:bg-primary-50 transition-colors"
            >
              浏览资源
            </Link>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  )
}
