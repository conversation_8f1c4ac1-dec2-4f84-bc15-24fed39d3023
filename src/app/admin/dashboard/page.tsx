'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { 
  BarChart3, 
  Download, 
  FileText, 
  Users, 
  TrendingUp,
  Calendar,
  Eye,
  Plus
} from 'lucide-react'
import AdminLayout from '@/components/admin/AdminLayout'
import Button from '@/components/ui/Button'

// 模拟统计数据
const mockStats = {
  totalFiles: 456,
  totalDownloads: 12580,
  totalUsers: 1250,
  totalCategories: 15,
  todayDownloads: 89,
  weeklyGrowth: 12.5,
  monthlyGrowth: 8.3
}

// 模拟最近下载数据
const mockRecentDownloads = [
  {
    id: '1',
    fileName: '海能达 CPS 2.0 写频软件',
    downloadTime: '2024-01-15 14:30:25',
    userIP: '*************',
    fileSize: '15.2 MB'
  },
  {
    id: '2',
    fileName: '摩托罗拉 CPS 16.0 写频软件',
    downloadTime: '2024-01-15 14:25:18',
    userIP: '*********',
    fileSize: '85.4 MB'
  },
  {
    id: '3',
    fileName: '建伍 KPG-D1N 写频软件',
    downloadTime: '2024-01-15 14:20:12',
    userIP: '***********',
    fileSize: '24.1 MB'
  }
]

// 模拟热门文件数据
const mockPopularFiles = [
  {
    id: '1',
    title: '海能达 CPS 2.0 写频软件',
    downloads: 1250,
    category: '写频软件',
    trend: '+15%'
  },
  {
    id: '2',
    title: '摩托罗拉 CPS 16.0 写频软件',
    downloads: 2100,
    category: '写频软件',
    trend: '+8%'
  },
  {
    id: '3',
    title: '海能达 PD780 用户手册',
    downloads: 650,
    category: '产品手册',
    trend: '+22%'
  }
]

export default function AdminDashboard() {
  const [stats, setStats] = useState(mockStats)
  const [recentDownloads, setRecentDownloads] = useState(mockRecentDownloads)
  const [popularFiles, setPopularFiles] = useState(mockPopularFiles)

  useEffect(() => {
    // 这里应该从API获取实际数据
    // fetchDashboardData()
  }, [])

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* 页面标题 */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">仪表板</h1>
            <p className="text-gray-600">欢迎回到管理后台</p>
          </div>
          <div className="flex space-x-3">
            <Link href="/admin/files/upload">
              <Button>
                <Plus className="w-4 h-4 mr-2" />
                上传文件
              </Button>
            </Link>
          </div>
        </div>

        {/* 统计卡片 */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <div className="bg-white rounded-lg shadow-md border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">总文件数</p>
                <p className="text-2xl font-bold text-gray-900">{stats.totalFiles}</p>
              </div>
              <div className="bg-blue-100 p-3 rounded-full">
                <FileText className="w-6 h-6 text-blue-600" />
              </div>
            </div>
            <div className="mt-4 flex items-center text-sm">
              <TrendingUp className="w-4 h-4 text-green-500 mr-1" />
              <span className="text-green-600">+{stats.monthlyGrowth}%</span>
              <span className="text-gray-500 ml-1">本月</span>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-md border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">总下载量</p>
                <p className="text-2xl font-bold text-gray-900">{stats.totalDownloads.toLocaleString()}</p>
              </div>
              <div className="bg-green-100 p-3 rounded-full">
                <Download className="w-6 h-6 text-green-600" />
              </div>
            </div>
            <div className="mt-4 flex items-center text-sm">
              <TrendingUp className="w-4 h-4 text-green-500 mr-1" />
              <span className="text-green-600">+{stats.weeklyGrowth}%</span>
              <span className="text-gray-500 ml-1">本周</span>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-md border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">用户数量</p>
                <p className="text-2xl font-bold text-gray-900">{stats.totalUsers.toLocaleString()}</p>
              </div>
              <div className="bg-purple-100 p-3 rounded-full">
                <Users className="w-6 h-6 text-purple-600" />
              </div>
            </div>
            <div className="mt-4 flex items-center text-sm">
              <Calendar className="w-4 h-4 text-blue-500 mr-1" />
              <span className="text-gray-500">活跃用户</span>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-md border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">今日下载</p>
                <p className="text-2xl font-bold text-gray-900">{stats.todayDownloads}</p>
              </div>
              <div className="bg-orange-100 p-3 rounded-full">
                <BarChart3 className="w-6 h-6 text-orange-600" />
              </div>
            </div>
            <div className="mt-4 flex items-center text-sm">
              <Eye className="w-4 h-4 text-gray-500 mr-1" />
              <span className="text-gray-500">实时统计</span>
            </div>
          </div>
        </div>

        <div className="grid lg:grid-cols-2 gap-6">
          {/* 最近下载 */}
          <div className="bg-white rounded-lg shadow-md border border-gray-200 p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900">最近下载</h3>
              <Link href="/admin/downloads" className="text-primary-600 hover:text-primary-700 text-sm">
                查看全部
              </Link>
            </div>
            <div className="space-y-4">
              {recentDownloads.map((download) => (
                <div key={download.id} className="flex items-center justify-between py-3 border-b border-gray-100 last:border-b-0">
                  <div className="flex-1">
                    <p className="font-medium text-gray-900 text-sm">{download.fileName}</p>
                    <div className="flex items-center space-x-4 text-xs text-gray-500 mt-1">
                      <span>{download.downloadTime}</span>
                      <span>{download.userIP}</span>
                      <span>{download.fileSize}</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* 热门文件 */}
          <div className="bg-white rounded-lg shadow-md border border-gray-200 p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900">热门文件</h3>
              <Link href="/admin/files" className="text-primary-600 hover:text-primary-700 text-sm">
                查看全部
              </Link>
            </div>
            <div className="space-y-4">
              {popularFiles.map((file) => (
                <div key={file.id} className="flex items-center justify-between py-3 border-b border-gray-100 last:border-b-0">
                  <div className="flex-1">
                    <p className="font-medium text-gray-900 text-sm">{file.title}</p>
                    <div className="flex items-center space-x-4 text-xs text-gray-500 mt-1">
                      <span>{file.category}</span>
                      <span>{file.downloads.toLocaleString()} 次下载</span>
                    </div>
                  </div>
                  <div className="text-right">
                    <span className="text-green-600 text-sm font-medium">{file.trend}</span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* 快速操作 */}
        <div className="bg-white rounded-lg shadow-md border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">快速操作</h3>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Link href="/admin/files/upload">
              <div className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer">
                <Plus className="w-8 h-8 text-primary-600 mb-2" />
                <h4 className="font-medium text-gray-900">上传文件</h4>
                <p className="text-sm text-gray-600">添加新的软件或文档</p>
              </div>
            </Link>
            
            <Link href="/admin/categories">
              <div className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer">
                <FileText className="w-8 h-8 text-blue-600 mb-2" />
                <h4 className="font-medium text-gray-900">管理分类</h4>
                <p className="text-sm text-gray-600">组织文件分类结构</p>
              </div>
            </Link>
            
            <Link href="/admin/users">
              <div className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer">
                <Users className="w-8 h-8 text-green-600 mb-2" />
                <h4 className="font-medium text-gray-900">用户管理</h4>
                <p className="text-sm text-gray-600">管理用户账户</p>
              </div>
            </Link>
            
            <Link href="/admin/settings">
              <div className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer">
                <BarChart3 className="w-8 h-8 text-purple-600 mb-2" />
                <h4 className="font-medium text-gray-900">系统设置</h4>
                <p className="text-sm text-gray-600">配置网站参数</p>
              </div>
            </Link>
          </div>
        </div>
      </div>
    </AdminLayout>
  )
}
