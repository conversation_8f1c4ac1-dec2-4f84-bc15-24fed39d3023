interface StructuredDataProps {
  type: 'website' | 'organization' | 'software' | 'article' | 'breadcrumb'
  data: any
}

export default function StructuredData({ type, data }: StructuredDataProps) {
  const generateStructuredData = () => {
    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'
    
    switch (type) {
      case 'website':
        return {
          "@context": "https://schema.org",
          "@type": "WebSite",
          "name": "对讲机软件下载站",
          "description": "专业的对讲机行业软件下载平台，提供各品牌写频软件、产品资料、技术文档等文件下载服务",
          "url": baseUrl,
          "potentialAction": {
            "@type": "SearchAction",
            "target": {
              "@type": "EntryPoint",
              "urlTemplate": `${baseUrl}/search?q={search_term_string}`
            },
            "query-input": "required name=search_term_string"
          },
          "publisher": {
            "@type": "Organization",
            "name": "对讲机软件下载站",
            "url": baseUrl
          }
        }

      case 'organization':
        return {
          "@context": "https://schema.org",
          "@type": "Organization",
          "name": "对讲机软件下载站",
          "description": "专业的对讲机行业软件下载平台",
          "url": baseUrl,
          "logo": `${baseUrl}/logo.png`,
          "contactPoint": {
            "@type": "ContactPoint",
            "contactType": "customer service",
            "availableLanguage": "Chinese"
          },
          "sameAs": [
            // 这里可以添加社交媒体链接
          ]
        }

      case 'software':
        return {
          "@context": "https://schema.org",
          "@type": "SoftwareApplication",
          "name": data.title,
          "description": data.description,
          "applicationCategory": "UtilityApplication",
          "operatingSystem": data.operatingSystem || "Windows",
          "downloadUrl": `${baseUrl}/files/${data.id}`,
          "fileSize": data.size ? `${data.size} bytes` : undefined,
          "version": data.version,
          "datePublished": data.createdAt,
          "dateModified": data.updatedAt,
          "publisher": {
            "@type": "Organization",
            "name": "对讲机软件下载站"
          },
          "offers": {
            "@type": "Offer",
            "price": "0",
            "priceCurrency": "CNY",
            "availability": "https://schema.org/InStock"
          }
        }

      case 'article':
        return {
          "@context": "https://schema.org",
          "@type": "Article",
          "headline": data.title,
          "description": data.description,
          "datePublished": data.createdAt,
          "dateModified": data.updatedAt,
          "author": {
            "@type": "Organization",
            "name": "对讲机软件下载站"
          },
          "publisher": {
            "@type": "Organization",
            "name": "对讲机软件下载站",
            "logo": {
              "@type": "ImageObject",
              "url": `${baseUrl}/logo.png`
            }
          },
          "mainEntityOfPage": {
            "@type": "WebPage",
            "@id": data.url
          }
        }

      case 'breadcrumb':
        return {
          "@context": "https://schema.org",
          "@type": "BreadcrumbList",
          "itemListElement": data.items.map((item: any, index: number) => ({
            "@type": "ListItem",
            "position": index + 1,
            "name": item.name,
            "item": item.url
          }))
        }

      default:
        return null
    }
  }

  const structuredData = generateStructuredData()

  if (!structuredData) {
    return null
  }

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{
        __html: JSON.stringify(structuredData)
      }}
    />
  )
}
