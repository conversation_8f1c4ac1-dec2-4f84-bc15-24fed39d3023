'use client'

import { useState, useRef } from 'react'
import { Upload, X, FileText, AlertCircle, CheckCircle } from 'lucide-react'
import Button from '@/components/ui/Button'
import Input from '@/components/ui/Input'

interface FileUploadProps {
  onUploadSuccess?: (file: any) => void
  onUploadError?: (error: string) => void
}

interface UploadFile {
  file: File
  id: string
  progress: number
  status: 'pending' | 'uploading' | 'success' | 'error'
  error?: string
}

const ALLOWED_FILE_TYPES = [
  'application/zip',
  'application/x-rar-compressed', 
  'application/x-7z-compressed',
  'application/pdf',
  'application/msword',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  'application/vnd.ms-excel',
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  'application/x-msdownload',
  'text/plain',
  'text/csv'
]

const MAX_FILE_SIZE = 100 * 1024 * 1024 // 100MB

function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

export default function FileUpload({ onUploadSuccess, onUploadError }: FileUploadProps) {
  const [files, setFiles] = useState<UploadFile[]>([])
  const [isDragOver, setIsDragOver] = useState(false)
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    categoryId: '',
    tags: '',
    version: ''
  })
  const fileInputRef = useRef<HTMLInputElement>(null)

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragOver(true)
  }

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragOver(false)
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragOver(false)
    
    const droppedFiles = Array.from(e.dataTransfer.files)
    handleFiles(droppedFiles)
  }

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      const selectedFiles = Array.from(e.target.files)
      handleFiles(selectedFiles)
    }
  }

  const handleFiles = (newFiles: File[]) => {
    const validFiles: UploadFile[] = []
    
    newFiles.forEach(file => {
      // 验证文件类型
      if (!ALLOWED_FILE_TYPES.includes(file.type)) {
        onUploadError?.(`不支持的文件类型: ${file.name}`)
        return
      }
      
      // 验证文件大小
      if (file.size > MAX_FILE_SIZE) {
        onUploadError?.(`文件过大: ${file.name} (最大 ${formatFileSize(MAX_FILE_SIZE)})`)
        return
      }
      
      validFiles.push({
        file,
        id: Math.random().toString(36).substr(2, 9),
        progress: 0,
        status: 'pending'
      })
    })
    
    setFiles(prev => [...prev, ...validFiles])
  }

  const removeFile = (id: string) => {
    setFiles(prev => prev.filter(f => f.id !== id))
  }

  const uploadFile = async (uploadFile: UploadFile) => {
    const formDataToSend = new FormData()
    formDataToSend.append('file', uploadFile.file)
    formDataToSend.append('title', formData.title || uploadFile.file.name)
    formDataToSend.append('description', formData.description)
    formDataToSend.append('categoryId', formData.categoryId)
    formDataToSend.append('tags', formData.tags)
    formDataToSend.append('version', formData.version)

    try {
      // 更新状态为上传中
      setFiles(prev => prev.map(f => 
        f.id === uploadFile.id 
          ? { ...f, status: 'uploading', progress: 0 }
          : f
      ))

      const response = await fetch('/api/files', {
        method: 'POST',
        body: formDataToSend
      })

      const result = await response.json()

      if (result.success) {
        // 上传成功
        setFiles(prev => prev.map(f => 
          f.id === uploadFile.id 
            ? { ...f, status: 'success', progress: 100 }
            : f
        ))
        onUploadSuccess?.(result.data)
      } else {
        // 上传失败
        setFiles(prev => prev.map(f => 
          f.id === uploadFile.id 
            ? { ...f, status: 'error', error: result.error }
            : f
        ))
        onUploadError?.(result.error)
      }
    } catch (error) {
      // 网络错误
      setFiles(prev => prev.map(f => 
        f.id === uploadFile.id 
          ? { ...f, status: 'error', error: '网络错误' }
          : f
      ))
      onUploadError?.('网络错误')
    }
  }

  const uploadAllFiles = async () => {
    const pendingFiles = files.filter(f => f.status === 'pending')
    
    for (const file of pendingFiles) {
      await uploadFile(file)
    }
  }

  const clearAllFiles = () => {
    setFiles([])
  }

  return (
    <div className="space-y-6">
      {/* 文件信息表单 */}
      <div className="grid md:grid-cols-2 gap-4">
        <Input
          label="文件标题"
          placeholder="输入文件标题"
          value={formData.title}
          onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
        />
        <Input
          label="版本号"
          placeholder="如: 1.0.0"
          value={formData.version}
          onChange={(e) => setFormData(prev => ({ ...prev, version: e.target.value }))}
        />
        <div className="md:col-span-2">
          <Input
            label="文件描述"
            placeholder="输入文件描述"
            value={formData.description}
            onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            分类
          </label>
          <select
            value={formData.categoryId}
            onChange={(e) => setFormData(prev => ({ ...prev, categoryId: e.target.value }))}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
          >
            <option value="">选择分类</option>
            <option value="programming-software">写频软件</option>
            <option value="manuals">产品手册</option>
            <option value="technical-docs">技术资料</option>
            <option value="drivers">驱动程序</option>
            <option value="firmware">固件升级</option>
          </select>
        </div>
        <Input
          label="标签"
          placeholder="用逗号分隔多个标签"
          value={formData.tags}
          onChange={(e) => setFormData(prev => ({ ...prev, tags: e.target.value }))}
        />
      </div>

      {/* 文件拖拽区域 */}
      <div
        className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
          isDragOver 
            ? 'border-primary-500 bg-primary-50' 
            : 'border-gray-300 hover:border-gray-400'
        }`}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
      >
        <Upload className="w-12 h-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">
          拖拽文件到此处或点击选择
        </h3>
        <p className="text-gray-600 mb-4">
          支持 ZIP, RAR, 7Z, PDF, DOC, XLS, EXE 等格式，最大 {formatFileSize(MAX_FILE_SIZE)}
        </p>
        <Button
          variant="outline"
          onClick={() => fileInputRef.current?.click()}
        >
          选择文件
        </Button>
        <input
          ref={fileInputRef}
          type="file"
          multiple
          accept=".zip,.rar,.7z,.pdf,.doc,.docx,.xls,.xlsx,.exe,.msi,.txt,.csv"
          onChange={handleFileSelect}
          className="hidden"
        />
      </div>

      {/* 文件列表 */}
      {files.length > 0 && (
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h4 className="text-lg font-medium text-gray-900">
              待上传文件 ({files.length})
            </h4>
            <div className="space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={clearAllFiles}
              >
                清空
              </Button>
              <Button
                size="sm"
                onClick={uploadAllFiles}
                disabled={files.some(f => f.status === 'uploading')}
              >
                上传全部
              </Button>
            </div>
          </div>

          <div className="space-y-2">
            {files.map((uploadFile) => (
              <div key={uploadFile.id} className="bg-white border border-gray-200 rounded-lg p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <FileText className="w-5 h-5 text-gray-400" />
                    <div>
                      <div className="font-medium text-gray-900">{uploadFile.file.name}</div>
                      <div className="text-sm text-gray-500">
                        {formatFileSize(uploadFile.file.size)}
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    {uploadFile.status === 'success' && (
                      <CheckCircle className="w-5 h-5 text-green-500" />
                    )}
                    {uploadFile.status === 'error' && (
                      <AlertCircle className="w-5 h-5 text-red-500" />
                    )}
                    {uploadFile.status === 'uploading' && (
                      <div className="w-5 h-5 border-2 border-primary-600 border-t-transparent rounded-full animate-spin" />
                    )}
                    <button
                      onClick={() => removeFile(uploadFile.id)}
                      className="text-gray-400 hover:text-red-500"
                    >
                      <X className="w-4 h-4" />
                    </button>
                  </div>
                </div>
                
                {uploadFile.status === 'error' && uploadFile.error && (
                  <div className="mt-2 text-sm text-red-600">
                    错误: {uploadFile.error}
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}
