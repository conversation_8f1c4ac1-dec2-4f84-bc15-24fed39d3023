'use client'

import { useEffect } from 'react'

interface AdSenseProps {
  adSlot: string
  adFormat?: 'auto' | 'rectangle' | 'vertical' | 'horizontal'
  adLayout?: string
  adLayoutKey?: string
  style?: React.CSSProperties
  className?: string
  responsive?: boolean
}

declare global {
  interface Window {
    adsbygoogle: any[]
  }
}

export default function AdSense({
  adSlot,
  adFormat = 'auto',
  adLayout,
  adLayoutKey,
  style = { display: 'block' },
  className = '',
  responsive = true
}: AdSenseProps) {
  useEffect(() => {
    try {
      if (typeof window !== 'undefined') {
        (window.adsbygoogle = window.adsbygoogle || []).push({})
      }
    } catch (error) {
      console.error('AdSense error:', error)
    }
  }, [])

  // 如果没有配置AdSense客户端ID，显示占位符
  const adsenseClientId = process.env.NEXT_PUBLIC_ADSENSE_CLIENT_ID
  if (!adsenseClientId) {
    return (
      <div className={`bg-gray-100 border-2 border-dashed border-gray-300 rounded-lg p-8 text-center ${className}`}>
        <div className="text-gray-500">
          <div className="text-sm font-medium mb-2">广告位预留</div>
          <div className="text-xs">AdSense Slot: {adSlot}</div>
          <div className="text-xs">Format: {adFormat}</div>
        </div>
      </div>
    )
  }

  return (
    <div className={className}>
      <ins
        className="adsbygoogle"
        style={style}
        data-ad-client={adsenseClientId}
        data-ad-slot={adSlot}
        data-ad-format={adFormat}
        data-ad-layout={adLayout}
        data-ad-layout-key={adLayoutKey}
        data-full-width-responsive={responsive ? 'true' : 'false'}
      />
    </div>
  )
}

// 预定义的广告位组件
export function HeaderAd() {
  return (
    <AdSense
      adSlot="1234567890"
      adFormat="horizontal"
      className="my-4"
      style={{ display: 'block', height: '90px' }}
    />
  )
}

export function SidebarAd() {
  return (
    <AdSense
      adSlot="1234567891"
      adFormat="vertical"
      className="mb-6"
      style={{ display: 'block', width: '300px', height: '250px' }}
    />
  )
}

export function ContentAd() {
  return (
    <AdSense
      adSlot="1234567892"
      adFormat="rectangle"
      className="my-6"
      style={{ display: 'block', width: '336px', height: '280px', margin: '0 auto' }}
    />
  )
}

export function FooterAd() {
  return (
    <AdSense
      adSlot="1234567893"
      adFormat="horizontal"
      className="my-4"
      style={{ display: 'block', height: '90px' }}
    />
  )
}

export function MobileAd() {
  return (
    <AdSense
      adSlot="1234567894"
      adFormat="auto"
      className="my-4 block md:hidden"
      style={{ display: 'block' }}
    />
  )
}

export function DesktopAd() {
  return (
    <AdSense
      adSlot="1234567895"
      adFormat="auto"
      className="my-4 hidden md:block"
      style={{ display: 'block' }}
    />
  )
}
