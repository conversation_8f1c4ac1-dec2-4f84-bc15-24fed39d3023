import { PrismaClient } from '@prisma/client'
import bcrypt from 'bcryptjs'

const prisma = new PrismaClient()

async function main() {
  console.log('开始数据库种子数据...')

  // 创建管理员用户
  const adminEmail = process.env.ADMIN_EMAIL || '<EMAIL>'
  const adminPassword = process.env.ADMIN_PASSWORD || 'admin123'
  const hashedPassword = await bcrypt.hash(adminPassword, 12)

  const admin = await prisma.user.upsert({
    where: { email: adminEmail },
    update: {},
    create: {
      email: adminEmail,
      name: '管理员',
      role: 'ADMIN',
    },
  })

  console.log('管理员用户创建完成:', admin)

  // 创建分类
  const categories = [
    {
      name: '写频软件',
      slug: 'programming-software',
      description: '各品牌对讲机写频软件',
    },
    {
      name: '产品手册',
      slug: 'manuals',
      description: '产品使用手册和说明书',
    },
    {
      name: '技术资料',
      slug: 'technical-docs',
      description: '技术文档和参考资料',
    },
    {
      name: '驱动程序',
      slug: 'drivers',
      description: '设备驱动程序',
    },
    {
      name: '固件升级',
      slug: 'firmware',
      description: '设备固件升级文件',
    },
  ]

  for (const categoryData of categories) {
    const category = await prisma.category.upsert({
      where: { slug: categoryData.slug },
      update: {},
      create: categoryData,
    })
    console.log('分类创建完成:', category.name)
  }

  // 创建子分类
  const programmingSoftwareCategory = await prisma.category.findUnique({
    where: { slug: 'programming-software' },
  })

  if (programmingSoftwareCategory) {
    const subCategories = [
      {
        name: '海能达',
        slug: 'hytera',
        description: '海能达对讲机写频软件',
        parentId: programmingSoftwareCategory.id,
      },
      {
        name: '摩托罗拉',
        slug: 'motorola',
        description: '摩托罗拉对讲机写频软件',
        parentId: programmingSoftwareCategory.id,
      },
      {
        name: '建伍',
        slug: 'kenwood',
        description: '建伍对讲机写频软件',
        parentId: programmingSoftwareCategory.id,
      },
      {
        name: '八重洲',
        slug: 'yaesu',
        description: '八重洲对讲机写频软件',
        parentId: programmingSoftwareCategory.id,
      },
    ]

    for (const subCategoryData of subCategories) {
      const subCategory = await prisma.category.upsert({
        where: { slug: subCategoryData.slug },
        update: {},
        create: subCategoryData,
      })
      console.log('子分类创建完成:', subCategory.name)
    }
  }

  // 创建网站配置
  const siteConfigs = [
    {
      key: 'site_title',
      value: '对讲机软件下载站',
      description: '网站标题',
    },
    {
      key: 'site_description',
      value: '专业的对讲机行业软件下载平台，提供各品牌写频软件、产品资料、技术文档等文件下载服务。',
      description: '网站描述',
    },
    {
      key: 'site_keywords',
      value: '对讲机,写频软件,产品资料,技术文档,下载,软件',
      description: '网站关键词',
    },
    {
      key: 'max_file_size',
      value: '100000000',
      description: '最大文件上传大小（字节）',
    },
    {
      key: 'allowed_file_types',
      value: 'zip,rar,7z,pdf,doc,docx,xls,xlsx,exe,msi,txt,csv',
      description: '允许的文件类型',
    },
  ]

  for (const configData of siteConfigs) {
    const config = await prisma.siteConfig.upsert({
      where: { key: configData.key },
      update: { value: configData.value },
      create: configData,
    })
    console.log('网站配置创建完成:', config.key)
  }

  // 创建示例页面
  const pages = [
    {
      title: '关于我们',
      slug: 'about',
      content: '我们是专业的对讲机软件下载平台...',
      metaTitle: '关于我们 - 对讲机软件下载站',
      metaDescription: '了解我们的服务和使命',
      isPublished: true,
    },
    {
      title: '联系我们',
      slug: 'contact',
      content: '如有问题或建议，欢迎联系我们...',
      metaTitle: '联系我们 - 对讲机软件下载站',
      metaDescription: '联系我们获取帮助和支持',
      isPublished: true,
    },
    {
      title: '隐私政策',
      slug: 'privacy',
      content: '我们重视您的隐私...',
      metaTitle: '隐私政策 - 对讲机软件下载站',
      metaDescription: '了解我们的隐私保护政策',
      isPublished: true,
    },
    {
      title: '使用条款',
      slug: 'terms',
      content: '使用本网站即表示您同意以下条款...',
      metaTitle: '使用条款 - 对讲机软件下载站',
      metaDescription: '网站使用条款和规定',
      isPublished: true,
    },
  ]

  for (const pageData of pages) {
    const page = await prisma.page.upsert({
      where: { slug: pageData.slug },
      update: {},
      create: pageData,
    })
    console.log('页面创建完成:', page.title)
  }

  console.log('数据库种子数据完成!')
}

main()
  .then(async () => {
    await prisma.$disconnect()
  })
  .catch(async (e) => {
    console.error(e)
    await prisma.$disconnect()
    process.exit(1)
  })
