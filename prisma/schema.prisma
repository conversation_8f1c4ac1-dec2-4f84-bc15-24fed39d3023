// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// 用户模型
model User {
  id        String   @id @default(cuid())
  email     String   @unique
  name      String?
  role      Role     @default(USER)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // 关联
  uploadedFiles File[]
  downloads     DownloadRecord[]

  @@map("users")
}

// 用户角色枚举
enum Role {
  ADMIN
  USER
}

// 文件分类模型
model Category {
  id          String   @id @default(cuid())
  name        String
  slug        String   @unique
  description String?
  parentId    String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // 自关联
  parent   Category?  @relation("CategoryHierarchy", fields: [parentId], references: [id])
  children Category[] @relation("CategoryHierarchy")

  // 关联
  files File[]

  @@map("categories")
}

// 文件模型
model File {
  id           String   @id @default(cuid())
  title        String
  description  String?
  filename     String   @unique
  originalName String
  mimeType     String
  size         Int
  downloadCount Int     @default(0)
  isActive     Boolean  @default(true)
  categoryId   String
  uploadedById String?
  tags         String[]
  version      String?
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  // 关联
  category     Category         @relation(fields: [categoryId], references: [id])
  uploadedBy   User?            @relation(fields: [uploadedById], references: [id])
  downloads    DownloadRecord[]

  @@map("files")
}

// 下载记录模型
model DownloadRecord {
  id        String   @id @default(cuid())
  fileId    String
  userId    String?
  ipAddress String
  userAgent String?
  createdAt DateTime @default(now())

  // 关联
  file File  @relation(fields: [fileId], references: [id])
  user User? @relation(fields: [userId], references: [id])

  @@map("download_records")
}

// 网站配置模型
model SiteConfig {
  id          String   @id @default(cuid())
  key         String   @unique
  value       String
  description String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@map("site_configs")
}

// 页面内容模型
model Page {
  id          String   @id @default(cuid())
  title       String
  slug        String   @unique
  content     String
  metaTitle   String?
  metaDescription String?
  isPublished Boolean  @default(false)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@map("pages")
}
